# SuccessionPlan AI - Local Development Environment Configuration

APP_NAME="SuccessionPlan AI"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost:8000

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file

BCRYPT_ROUNDS=10

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=succession_plan
DB_USERNAME=root
DB_PASSWORD=

# Session Configuration
SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

# Broadcasting
BROADCAST_CONNECTION=log

# Filesystem
FILESYSTEM_DISK=local

# Queue Configuration (Local Development)
QUEUE_CONNECTION=sync
# For testing queue features locally, use:
# QUEUE_CONNECTION=database
# QUEUE_CONNECTION=redis

# Cache Configuration
CACHE_STORE=file
CACHE_PREFIX=

# Redis Configuration (for queue testing)
REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail Configuration (Local Development)
MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# For testing email features, consider using Mailtrap:
# MAIL_MAILER=smtp
# MAIL_HOST=smtp.mailtrap.io
# MAIL_PORT=2525
# MAIL_USERNAME=your_mailtrap_username
# MAIL_PASSWORD=your_mailtrap_password

# AWS Configuration (for local S3 testing)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ORGANIZATION=

# External Search API Configuration
PERPLEXITY_API_KEY=your_perplexity_api_key_here

# Frontend Build Configuration
VITE_APP_NAME="${APP_NAME}"
VITE_APP_ENV="${APP_ENV}"

# Laravel Horizon Configuration (for local testing)
HORIZON_PREFIX=successionplan_local

# Development Tools
TELESCOPE_ENABLED=true
DEBUGBAR_ENABLED=true

# Local Development Features
ENABLE_QUERY_LOGGING=true
ENABLE_DEBUG_TOOLBAR=true

# Testing Configuration
TESTING_DATABASE=succession_plan_testing
