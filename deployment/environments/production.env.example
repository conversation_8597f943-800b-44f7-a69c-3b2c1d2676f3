# SuccessionPlan AI - Production Environment Configuration

APP_NAME="SuccessionPlan AI"
APP_ENV=production
APP_KEY=base64:your_secure_production_app_key_here
APP_DEBUG=false
APP_TIMEZONE=UTC
APP_URL=https://app.successionplan.ai

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=daily,slack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Database Configuration - Production
DB_CONNECTION=mysql
DB_HOST=production-db-cluster.amazonaws.com
DB_PORT=3306
DB_DATABASE=successionplan_production
DB_USERNAME=prod_user
DB_PASSWORD=your_highly_secure_production_db_password

# Read Replica for Performance
DB_READ_HOST=production-db-read-replica.amazonaws.com

# Session Configuration - Production
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=true
SESSION_PATH=/
SESSION_DOMAIN=.successionplan.ai

# Broadcasting - Production
BROADCAST_CONNECTION=redis

# Filesystem - S3 for production
FILESYSTEM_DISK=s3

# Queue Configuration - Production (Redis with clustering)
QUEUE_CONNECTION=redis
QUEUE_FAILED_DRIVER=database
QUEUE_TIMEOUT=3600

# Cache Configuration - Redis cluster for production
CACHE_STORE=redis
CACHE_PREFIX=prod_sp

# Redis Configuration - Production Cluster
REDIS_CLIENT=phpredis
REDIS_HOST=production-redis-cluster.amazonaws.com
REDIS_PASSWORD=your_secure_redis_password
REDIS_PORT=6379
REDIS_PREFIX=production_successionplan
REDIS_CLUSTER=true

# Mail Configuration - Production (SES)
MAIL_MAILER=ses
MAIL_HOST=
MAIL_PORT=
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# AWS Configuration - Production
AWS_ACCESS_KEY_ID=your_production_aws_access_key
AWS_SECRET_ACCESS_KEY=your_production_aws_secret_key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=successionplan-production-assets
AWS_USE_PATH_STYLE_ENDPOINT=false

# CloudFront CDN
AWS_CLOUDFRONT_DISTRIBUTION_ID=your_cloudfront_distribution_id
AWS_CLOUDFRONT_DOMAIN=cdn.successionplan.ai

# OpenAI Configuration - Production
OPENAI_API_KEY=your_production_openai_api_key
OPENAI_ORGANIZATION=your_openai_org_id
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=4000

# External Search API Configuration - Production
PERPLEXITY_API_KEY=your_production_perplexity_api_key

# Frontend Build Configuration
VITE_APP_NAME="${APP_NAME}"
VITE_APP_ENV="${APP_ENV}"
VITE_APP_URL="${APP_URL}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Laravel Horizon Configuration - Production
HORIZON_PREFIX=successionplan_production
HORIZON_DASHBOARD_MIDDLEWARE=web,auth,admin
HORIZON_MEMORY_LIMIT=512

# Production Security
SECURE_COOKIES=true
CSRF_COOKIE_SECURE=true
SESSION_SECURE_COOKIE=true
HSTS_ENABLED=true

# Performance Optimization
OPCACHE_ENABLED=true
VIEW_CACHE_ENABLED=true
CONFIG_CACHE_ENABLED=true
ROUTE_CACHE_ENABLED=true

# Monitoring and Logging
SENTRY_LARAVEL_DSN=your_production_sentry_dsn
NEW_RELIC_LICENSE_KEY=your_newrelic_license
DATADOG_API_KEY=your_datadog_api_key

# Queue Health Monitoring
QUEUE_HEALTH_CHECK_ENABLED=true
QUEUE_ALERT_WEBHOOK=https://hooks.slack.com/your/production/webhook
QUEUE_ALERT_EMAIL=<EMAIL>

# Backup Configuration
BACKUP_DISK=s3
BACKUP_RETENTION_DAYS=90
BACKUP_NOTIFICATION_EMAIL=<EMAIL>
BACKUP_ENCRYPTION_PASSWORD=your_backup_encryption_password

# Rate Limiting - Production
RATE_LIMIT_PER_MINUTE=60
API_RATE_LIMIT=500
THROTTLE_REQUESTS_PER_MINUTE=60

# SSL/TLS Configuration
SSL_REDIRECT=true
MIXED_CONTENT_UPGRADE=true

# Database Connection Pooling
DB_CONNECTION_POOL_SIZE=20
DB_CONNECTION_TIMEOUT=30

# Redis Connection Pooling
REDIS_CONNECTION_POOL_SIZE=10
REDIS_CONNECTION_TIMEOUT=5

# Search Service Configuration
ELASTICSEARCH_HOST=production-elasticsearch.amazonaws.com
ELASTICSEARCH_PORT=443
ELASTICSEARCH_SCHEME=https

# CDN Configuration
CDN_URL=https://cdn.successionplan.ai
ASSET_VERSION=v1.0.0

# External Integrations
STRIPE_KEY=your_production_stripe_key
STRIPE_SECRET=your_production_stripe_secret
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

PUSHER_APP_ID=your_pusher_app_id
PUSHER_APP_KEY=your_pusher_app_key
PUSHER_APP_SECRET=your_pusher_app_secret
PUSHER_APP_CLUSTER=us2

# Analytics
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
MIXPANEL_TOKEN=your_mixpanel_token

# Compliance and Security
GDPR_COMPLIANCE=true
COOKIE_CONSENT_REQUIRED=true
DATA_RETENTION_DAYS=2555

# Maintenance Windows
MAINTENANCE_MODE_SECRET=your_maintenance_secret_key
MAINTENANCE_ALLOWED_IPS=your.admin.ip.address

# Feature Flags
FEATURE_ADVANCED_SEARCH=true
FEATURE_AI_RECOMMENDATIONS=true
FEATURE_BULK_OPERATIONS=true

# Performance Tuning
PHP_MEMORY_LIMIT=512M
PHP_MAX_EXECUTION_TIME=300
NGINX_WORKER_PROCESSES=auto
NGINX_WORKER_CONNECTIONS=1024

# Database Performance
MYSQL_INNODB_BUFFER_POOL_SIZE=2G
MYSQL_QUERY_CACHE_SIZE=256M

# Production Alerts
ALERT_CPU_THRESHOLD=80
ALERT_MEMORY_THRESHOLD=85
ALERT_DISK_THRESHOLD=90
ALERT_QUEUE_SIZE_THRESHOLD=1000
