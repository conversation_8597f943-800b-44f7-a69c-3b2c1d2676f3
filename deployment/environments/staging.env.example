# SuccessionPlan AI - Staging Environment Configuration

APP_NAME="SuccessionPlan AI (Staging)"
APP_ENV=staging
APP_KEY=base64:your_generated_app_key_here
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=https://staging.successionplan.ai

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single,daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Database Configuration - Staging
DB_CONNECTION=mysql
DB_HOST=staging-db-host.amazonaws.com
DB_PORT=3306
DB_DATABASE=successionplan_staging
DB_USERNAME=staging_user
DB_PASSWORD=your_staging_db_password

# Session Configuration
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=.successionplan.ai

# Broadcasting
BROADCAST_CONNECTION=redis

# Filesystem - Use S3 for staging
FILESYSTEM_DISK=s3

# Queue Configuration - Staging (Redis)
QUEUE_CONNECTION=redis
QUEUE_FAILED_DRIVER=database

# Cache Configuration - Redis for staging
CACHE_STORE=redis
CACHE_PREFIX=staging_sp

# Redis Configuration - Staging
REDIS_CLIENT=phpredis
REDIS_HOST=staging-redis-host.amazonaws.com
REDIS_PASSWORD=your_redis_password
REDIS_PORT=6379
REDIS_PREFIX=staging_successionplan

# Mail Configuration - Staging (Use testing service)
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=your_mailtrap_username
MAIL_PASSWORD=your_mailtrap_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# AWS Configuration - Staging S3 Bucket
AWS_ACCESS_KEY_ID=your_staging_aws_access_key
AWS_SECRET_ACCESS_KEY=your_staging_aws_secret_key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=successionplan-staging-assets
AWS_USE_PATH_STYLE_ENDPOINT=false

# OpenAI Configuration - Use test API keys
OPENAI_API_KEY=your_openai_test_api_key
OPENAI_ORGANIZATION=your_openai_org_id

# External Search API Configuration - Test keys
PERPLEXITY_API_KEY=your_perplexity_test_api_key

# Frontend Build Configuration
VITE_APP_NAME="${APP_NAME}"
VITE_APP_ENV="${APP_ENV}"
VITE_APP_URL="${APP_URL}"

# Laravel Horizon Configuration - Staging
HORIZON_PREFIX=successionplan_staging
HORIZON_DASHBOARD_MIDDLEWARE=web,auth

# Staging-specific Features
ENABLE_QUERY_LOGGING=true
ENABLE_DEBUG_TOOLBAR=false
TELESCOPE_ENABLED=true

# Queue Health Monitoring
QUEUE_HEALTH_CHECK_ENABLED=true
QUEUE_ALERT_WEBHOOK=https://hooks.slack.com/your/staging/webhook

# Performance Monitoring
SENTRY_LARAVEL_DSN=your_sentry_staging_dsn

# Testing Configuration
ENABLE_AUTOMATED_TESTS=true
PHPUNIT_PARALLEL=true

# Backup Configuration
BACKUP_DISK=s3
BACKUP_NOTIFICATION_EMAIL=<EMAIL>

# Rate Limiting (more lenient for staging)
RATE_LIMIT_PER_MINUTE=120
API_RATE_LIMIT=1000

# Security (less strict for staging testing)
SECURE_COOKIES=true
CSRF_COOKIE_SECURE=true
SESSION_SECURE_COOKIE=true

# Development Helper
STAGING_PASSWORD=your_staging_access_password
