# SuccessionPlan AI - Deployment Guide

This guide provides comprehensive deployment instructions for all environments (local, staging, production) with best practices and safety measures.

## Overview

Our deployment system implements:
- **Zero-downtime deployments** for staging and production
- **Comprehensive health checks** and automatic rollback
- **Environment-specific configurations** and safety measures
- **Advanced queue management** with Horizon and Redis
- **Automated testing** and verification processes

## Quick Start

### Local Development
```bash
# Clone and setup
git clone https://github.com/SuccessionplanAI/successionplan-ai.git
cd successionplan-ai

# Run local deployment script
chmod +x scripts/deploy-local.sh
./scripts/deploy-local.sh

# Or for fresh installation with seeding
./scripts/deploy-local.sh install
```

### Staging Deployment
```bash
# Manual deployment
./scripts/deploy-staging.sh

# Via GitHub Actions (automatic on push to staging branch)
git push origin staging
```

### Production Deployment
```bash
# Manual deployment (requires confirmation)
./scripts/deploy-production.sh

# Via GitHub Actions (automatic on push to main branch)
git push origin main
```

## Environment Details

### Local Development

**Purpose**: Development and testing on local machines
**Features**:
- Simplified dependency management
- Optional database seeding
- Horizon queue management (optional)
- Hot reloading for assets

**Configuration**:
```bash
# Copy environment file
cp deployment/environments/local.env.example .env

# Edit database and API configurations
vi .env

# Run deployment
./scripts/deploy-local.sh
```

**Commands**:
```bash
# Full installation
./scripts/deploy-local.sh install

# Update only
./scripts/deploy-local.sh update

# Fresh database
./scripts/deploy-local.sh fresh

# Dependencies only
./scripts/deploy-local.sh deps
```

### Staging Environment

**Purpose**: Testing and client preview
**Features**:
- Zero-downtime deployment
- Automated testing
- Health monitoring
- Rollback capabilities
- Performance testing

**Infrastructure Requirements**:
- Ubuntu 20.04+ server
- PHP 8.3-FPM
- Nginx
- MySQL 8.0+
- Redis 6.0+
- Node.js 18+
- Supervisor

**Deployment Process**:
1. Pre-deployment health checks
2. Create new release directory
3. Install dependencies and build assets
4. Run database migrations
5. Execute automated tests
6. Atomic symlink switch
7. Service restart and queue management
8. Post-deployment verification

**Environment Variables**:
```bash
# Copy staging template
cp deployment/environments/staging.env.example /var/www/successionplanai/shared/.env

# Configure staging-specific values
sudo vi /var/www/successionplanai/shared/.env
```

### Production Environment

**Purpose**: Live application serving real users
**Features**:
- Maximum safety with confirmation prompts
- Comprehensive backup before deployment
- Enhanced health checks and monitoring
- Emergency rollback procedures
- Performance optimization
- Security hardening

**Safety Measures**:
- Mandatory deployment confirmation
- Pre-deployment backup creation
- Service health verification
- Performance threshold checks
- Automated rollback on failure
- Critical alert notifications

**Deployment Process**:
1. **Confirmation**: Manual confirmation required
2. **Health Checks**: Comprehensive system verification
3. **Backup**: Full application and database backup
4. **Maintenance Mode**: User notification during deployment
5. **Queue Management**: Graceful worker shutdown
6. **Release Creation**: New release in isolated directory
7. **Testing**: Full test suite execution
8. **Atomic Switch**: Instant symlink update
9. **Service Restart**: Production service management
10. **Verification**: Multi-layer health verification
11. **Monitoring**: Continuous post-deployment monitoring

## Queue Management

### Horizon Configuration

Our deployment includes advanced queue management with Laravel Horizon:

**Peak Hours Configuration** (8 AM - 6 PM weekdays):
- 8 workers for general queues
- 6 workers for search operations
- 4 workers for bulk operations

**Off-Peak Configuration**:
- 2 workers for general queues
- 2 workers for search operations
- 1 worker for maintenance

**Queue Priorities**:
1. `high` - Critical operations
2. `search` - Candidate search jobs
3. `default` - Standard operations
4. `bulk` - Batch processing
5. `low` - Background tasks

### Automated Scaling

Workers automatically scale based on:
- Time of day (peak vs off-peak)
- Queue size (emergency scaling)
- System resources (CPU, memory)
- Service availability

## Monitoring and Health Checks

### Health Check Endpoints

- **Application Health**: `/health`
- **Database**: Connection and query performance
- **Redis**: Connection and response time
- **Queue System**: Horizon status and worker health

### Automated Monitoring

The deployment includes automated monitoring scripts:

```bash
# Run health monitoring
./scripts/queue-health-monitor.sh

# Check system status
./scripts/queue-health-monitor.sh report

# Manual worker restart
./scripts/queue-health-monitor.sh restart-workers
```

### Alerting

Alerts are sent for:
- High queue sizes (>100 jobs)
- Critical queue sizes (>500 jobs)
- Failed job accumulation (>50 failures)
- Service unavailability
- Performance degradation

## Rollback Procedures

### Automatic Rollback

Deployments automatically rollback on:
- Health check failures
- Test failures (if enabled)
- Service restart failures
- Application errors during deployment

### Manual Rollback

```bash
# Staging rollback
./scripts/deploy-staging.sh rollback

# Production rollback (requires confirmation)
./scripts/deploy-production.sh rollback

# Check deployment status
./scripts/deploy-production.sh status
```

## Security Best Practices

### Environment Security

1. **Separate Environment Files**: Each environment has isolated configuration
2. **Secret Management**: Sensitive data stored in secure environment variables
3. **Access Control**: SSH key-based authentication with limited permissions
4. **SSL/TLS**: HTTPS enforcement in staging and production
5. **Security Headers**: HSTS, CSP, and other security headers

### Deployment Security

1. **Confirmation Requirements**: Production deployments require explicit confirmation
2. **Backup Encryption**: Production backups are encrypted
3. **Audit Logging**: All deployment activities are logged
4. **Permission Validation**: User and permission checks before deployment

## Troubleshooting

### Common Issues

**1. Database Connection Failures**
```bash
# Check database connectivity
php artisan tinker --execute="DB::connection()->getPdo();"

# Verify environment configuration
grep DB_ .env
```

**2. Queue Processing Issues**
```bash
# Check Horizon status
php artisan horizon:status

# Restart queue workers
php artisan queue:restart

# Monitor queue health
./scripts/queue-health-monitor.sh
```

**3. Asset Build Failures**
```bash
# Clear Node modules and reinstall
rm -rf node_modules package-lock.json
npm install --legacy-peer-deps

# Rebuild assets
npm run build
```

**4. Permission Issues**
```bash
# Fix storage permissions
sudo chown -R www-data:www-data storage bootstrap/cache
sudo chmod -R 775 storage bootstrap/cache
```

### Emergency Procedures

**1. Application Down**
```bash
# Immediate rollback
./scripts/deploy-production.sh rollback

# Check service status
systemctl status nginx php8.3-fpm mysql redis-server

# Review error logs
tail -f storage/logs/laravel.log
```

**2. Database Issues**
```bash
# Restore from backup
# (Follow specific backup restoration procedures)

# Check database performance
mysql -e "SHOW PROCESSLIST;"
```

**3. Queue System Failure**
```bash
# Emergency queue restart
./scripts/queue-health-monitor.sh restart-workers
./scripts/queue-health-monitor.sh restart-horizon

# Check Redis connectivity
redis-cli ping
```

## Performance Optimization

### Production Optimizations

1. **OPcache**: PHP bytecode caching enabled
2. **Config Caching**: Laravel configuration cached
3. **Route Caching**: Route definitions cached
4. **View Caching**: Blade templates compiled
5. **Asset Optimization**: Minified and compressed assets
6. **Database Optimization**: Connection pooling and query optimization
7. **Redis Clustering**: Distributed cache and session storage

### Monitoring Performance

```bash
# Check application performance
./scripts/deploy-production.sh health

# Monitor queue performance
./scripts/queue-health-monitor.sh report

# System resource monitoring
htop
iostat -x 1
```

## Backup and Recovery

### Automated Backups

- **Database**: Daily automated backups with 90-day retention
- **Application Files**: Pre-deployment backups
- **User Uploads**: Continuous S3 backup with versioning

### Manual Backup

```bash
# Create manual backup
php artisan backup:run

# Database only
php artisan backup:run --only-db

# Files only
php artisan backup:run --only-files
```

### Recovery Procedures

1. **Database Recovery**: Restore from automated daily backups
2. **Application Recovery**: Rollback to previous release
3. **File Recovery**: Restore from S3 versioned storage

## Continuous Integration

### GitHub Actions Workflows

- **Staging Deployment**: Automatic on push to `staging` branch
- **Production Deployment**: Automatic on push to `main` branch with safety checks
- **Testing**: Automated test execution before deployment
- **Security Scanning**: Dependency and security vulnerability checks

### Manual Workflow Triggers

```bash
# Trigger staging deployment
gh workflow run deploy-staging.yml

# Trigger production deployment with options
gh workflow run deploy-production.yml \
  -f maintenance_window=true \
  -f skip_tests=false \
  -f force_deploy=false
```

## Support and Maintenance

### Regular Maintenance Tasks

1. **Weekly**: Review deployment logs and performance metrics
2. **Monthly**: Update dependencies and security patches
3. **Quarterly**: Capacity planning and infrastructure review
4. **Annually**: Security audit and disaster recovery testing

### Contact Information

- **Technical Lead**: Paul Elliot (<EMAIL>)
- **DevOps Team**: Available via Slack #devops channel
- **Emergency Contact**: Emergency escalation procedures documented separately

---

**Note**: This deployment system is designed for enterprise-grade reliability and security. Always test changes in staging before production deployment, and maintain regular backups of all critical data.
