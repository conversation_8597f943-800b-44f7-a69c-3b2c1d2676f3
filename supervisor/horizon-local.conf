# Supervisor configuration for Laravel Horizon - Local Development
# Place this file in /etc/supervisor/conf.d/ or your supervisor config directory

[program:horizon-local]
process_name=%(program_name)s
command=php /path/to/your/project/artisan horizon
directory=/path/to/your/project
autostart=true
autorestart=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/path/to/your/project/storage/logs/horizon-supervisor.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=3
stopwaitsecs=3600
killasgroup=true
priority=999
startsecs=3
startretries=3

# Environment variables for local development
environment=LARAVEL_ENV="local"

[group:laravel-workers-local]
programs=horizon-local
