# Supervisor configuration for Laravel Horizon - Production Environment
# Place this file in /etc/supervisor/conf.d/

[program:horizon-production]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/production/artisan horizon
directory=/var/www/production
autostart=true
autorestart=true
user=www-data
numprocs=3
redirect_stderr=true
stdout_logfile=/var/www/production/storage/logs/horizon-supervisor.log
stderr_logfile=/var/www/production/storage/logs/horizon-supervisor-error.log
stdout_logfile_maxbytes=200MB
stderr_logfile_maxbytes=200MB
stdout_logfile_backups=10
stderr_logfile_backups=10
stopwaitsecs=3600
killasgroup=true
priority=999
startsecs=5
startretries=5

# Environment variables for production
environment=LARAVEL_ENV="production"

# Resource limits for production
minfds=4096
minprocs=1000

# Memory and CPU limits
rlimit_as=2147483648  # 2GB memory limit
rlimit_core=0         # No core dumps

[group:laravel-workers-production]
programs=horizon-production
priority=999
