# Supervisor configuration for Laravel Horizon - Staging Environment
# Place this file in /etc/supervisor/conf.d/

[program:horizon-staging]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/staging/artisan horizon
directory=/var/www/staging
autostart=true
autorestart=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/staging/storage/logs/horizon-supervisor.log
stdout_logfile_maxbytes=100MB
stdout_logfile_backups=5
stopwaitsecs=3600
killasgroup=true
priority=999
startsecs=3
startretries=3

# Environment variables for staging
environment=LARAVEL_ENV="staging"

# Resource limits
minfds=1024
minprocs=200

[group:laravel-workers-staging]
programs=horizon-staging
