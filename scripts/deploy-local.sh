#!/bin/bash

# Local Development Deployment Script for SuccessionPlan AI
# This script handles local environment setup and updates

set -e

# Configuration
APP_ROOT=${APP_ROOT:-$(pwd)}
LOG_FILE="$APP_ROOT/storage/logs/local-deployment.log"
PHP_VERSION=${PHP_VERSION:-8.3}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] ✓${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] ⚠${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ✗${NC} $1" | tee -a "$LOG_FILE"
}

# Check if we're in a Laravel project
check_laravel_project() {
    if [[ ! -f "artisan" ]]; then
        log_error "Not in a Laravel project directory"
        exit 1
    fi
}

# Check dependencies
check_dependencies() {
    log "Checking dependencies..."
    
    # Check PHP
    if ! command -v php &> /dev/null; then
        log_error "PHP is not installed"
        exit 1
    fi
    
    # Check Composer
    if ! command -v composer &> /dev/null; then
        log_error "Composer is not installed"
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi
    
    # Check NPM
    if ! command -v npm &> /dev/null; then
        log_error "NPM is not installed"
        exit 1
    fi
    
    log_success "All dependencies are available"
}

# Setup environment file
setup_environment() {
    log "Setting up environment file..."
    
    if [[ ! -f ".env" ]]; then
        if [[ -f ".env.example" ]]; then
            cp .env.example .env
            log_success "Created .env file from .env.example"
        else
            log_error ".env.example not found"
            exit 1
        fi
    else
        log "Environment file already exists"
    fi
    
    # Generate application key if not set
    if ! grep -q "APP_KEY=base64:" .env; then
        php artisan key:generate
        log_success "Generated application key"
    fi
}

# Install PHP dependencies
install_php_dependencies() {
    log "Installing PHP dependencies..."
    
    if [[ -f "composer.lock" ]]; then
        composer install
    else
        composer update
    fi
    
    log_success "PHP dependencies installed"
}

# Install Node.js dependencies
install_node_dependencies() {
    log "Installing Node.js dependencies..."
    
    # Remove node_modules if package-lock.json changed
    if [[ -f "package-lock.json" ]] && [[ -d "node_modules" ]]; then
        local lock_modified=$(stat -c %Y package-lock.json 2>/dev/null || stat -f %m package-lock.json 2>/dev/null)
        local modules_modified=$(stat -c %Y node_modules 2>/dev/null || stat -f %m node_modules 2>/dev/null || echo 0)
        
        if [[ $lock_modified -gt $modules_modified ]]; then
            log "Package lock file is newer, cleaning node_modules..."
            rm -rf node_modules
        fi
    fi
    
    npm install --legacy-peer-deps
    
    # Install additional packages mentioned in deployment script
    npm install @inertiajs/inertia sweetalert2 react-window
    
    log_success "Node.js dependencies installed"
}

# Setup database
setup_database() {
    log "Setting up database..."
    
    # Check if database connection works
    if php artisan tinker --execute="DB::connection()->getPdo();" &>/dev/null; then
        log "Database connection successful"
        
        # Run migrations
        php artisan migrate
        log_success "Database migrations completed"
        
        # Run seeders if requested
        if [[ "${1:-}" == "--seed" ]] || [[ "${SEED_DB:-}" == "true" ]]; then
            php artisan db:seed
            log_success "Database seeded"
        fi
    else
        log_warning "Database connection failed - skipping migrations"
        log_warning "Please check your database configuration in .env"
    fi
}

# Build assets
build_assets() {
    log "Building frontend assets..."
    
    npm run build
    
    log_success "Assets built successfully"
}

# Setup storage and permissions
setup_storage() {
    log "Setting up storage and permissions..."
    
    # Create storage link if it doesn't exist
    if [[ ! -L "public/storage" ]]; then
        php artisan storage:link
        log_success "Storage link created"
    fi
    
    # Set permissions for Unix-like systems
    if [[ "$OSTYPE" != "msys" ]] && [[ "$OSTYPE" != "win32" ]]; then
        chmod -R 775 storage bootstrap/cache
        log_success "Storage permissions set"
    fi
}

# Setup queue system
setup_queues() {
    log "Setting up queue system..."
    
    # Create queue tables if using database driver
    local queue_connection=$(grep "QUEUE_CONNECTION=" .env | cut -d'=' -f2)
    
    if [[ "$queue_connection" == "database" ]]; then
        if ! php artisan queue:table &>/dev/null; then
            log "Queue table migration already exists"
        else
            php artisan migrate
            log_success "Queue tables created"
        fi
    fi
    
    # Create failed jobs table
    if ! php artisan queue:failed-table &>/dev/null; then
        log "Failed jobs table migration already exists"
    else
        php artisan migrate
        log_success "Failed jobs table created"
    fi
}

# Clear caches
clear_caches() {
    log "Clearing application caches..."
    
    php artisan cache:clear
    php artisan config:clear
    php artisan route:clear
    php artisan view:clear
    
    log_success "Caches cleared"
}

# Start development services
start_development_services() {
    log "Starting development services..."
    
    # Check if Horizon should be started
    local queue_connection=$(grep "QUEUE_CONNECTION=" .env | cut -d'=' -f2)
    
    if [[ "$queue_connection" == "redis" ]]; then
        # Check if Redis is available
        if command -v redis-cli &> /dev/null && redis-cli ping &>/dev/null; then
            log "Redis is available for Horizon"
            
            # Start Horizon in background if not already running
            if ! pgrep -f "horizon" > /dev/null; then
                nohup php artisan horizon > storage/logs/horizon.log 2>&1 &
                log_success "Horizon started in background"
            else
                log "Horizon is already running"
            fi
        else
            log_warning "Redis not available - falling back to sync queue driver"
        fi
    fi
    
    log_success "Development services checked"
}

# Show completion summary
show_summary() {
    echo
    log_success "=== LOCAL DEPLOYMENT COMPLETED ==="
    log "Environment: local development"
    log "PHP Version: $(php -v | head -n1)"
    log "Laravel Version: $(php artisan --version)"
    log "Node Version: $(node --version)"
    log "NPM Version: $(npm --version)"
    
    # Show application URLs
    local app_url=$(grep "APP_URL=" .env | cut -d'=' -f2)
    log "Application URL: $app_url"
    log "Admin Panel: $app_url/admin (if applicable)"
    
    echo
    log "Next steps:"
    log "  1. Start the development server: php artisan serve"
    log "  2. Visit: http://localhost:8000"
    log "  3. Monitor queues: php artisan horizon:status"
    log "  4. View logs: tail -f storage/logs/laravel.log"
    echo
}

# Main deployment function
main() {
    local start_time=$(date +%s)
    
    log "Starting SuccessionPlan AI Local Deployment"
    log "Deployment started at: $(date)"
    
    check_laravel_project
    check_dependencies
    setup_environment
    install_php_dependencies
    install_node_dependencies
    setup_database "$@"
    build_assets
    setup_storage
    setup_queues
    clear_caches
    start_development_services
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    show_summary
    log_success "Local deployment completed in ${duration} seconds"
}

# Handle script arguments
case "${1:-}" in
    "install")
        log "Running full installation..."
        main --seed
        ;;
    "update")
        log "Running update only..."
        install_php_dependencies
        install_node_dependencies
        setup_database
        build_assets
        clear_caches
        log_success "Update completed"
        ;;
    "fresh")
        log "Running fresh installation..."
        php artisan migrate:fresh --seed
        clear_caches
        log_success "Fresh database setup completed"
        ;;
    "deps")
        log "Installing dependencies only..."
        install_php_dependencies
        install_node_dependencies
        log_success "Dependencies installed"
        ;;
    *)
        main "$@"
        ;;
esac
