#!/bin/bash

# Automated worker scaling script for SuccessionPlan AI
# This script dynamically scales Laravel queue workers based on time and queue size

set -e

# Configuration
PEAK_HOURS_START=8
PEAK_HOURS_END=18
REDIS_HOST=${REDIS_HOST:-127.0.0.1}
REDIS_PORT=${REDIS_PORT:-6379}
LOG_FILE="/var/www/html/storage/logs/worker-scaling.log"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Get current hour
CURRENT_HOUR=$(date +"%H")

# Get queue sizes from Redis
get_queue_size() {
    local queue_name=$1
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" llen "queues:$queue_name" 2>/dev/null || echo 0
}

# Check if it's a weekday
is_weekday() {
    local day=$(date +"%u")
    [[ "$day" -le 5 ]]
}

# Check if it's peak hours
is_peak_hours() {
    is_weekday && [[ "$CURRENT_HOUR" -ge "$PEAK_HOURS_START" ]] && [[ "$CURRENT_HOUR" -lt "$PEAK_HOURS_END" ]]
}

# Get total queue load
get_total_queue_load() {
    local high_queue=$(get_queue_size "high")
    local default_queue=$(get_queue_size "default")
    local search_queue=$(get_queue_size "search")
    local bulk_queue=$(get_queue_size "bulk")
    local total=$((high_queue + default_queue + search_queue + bulk_queue))
    echo "$total"
}

# Scale up workers for peak hours
scale_up_peak() {
    log "Scaling UP for peak hours..."
    
    # Stop off-peak workers
    supervisorctl stop laravel-workers-offpeak:* 2>/dev/null || true
    
    # Start peak workers
    supervisorctl start laravel-workers-peak:*
    
    # Wait for workers to start
    sleep 10
    
    # Verify workers are running
    local active_workers=$(supervisorctl status laravel-workers-peak:* | grep RUNNING | wc -l)
    log "Peak scaling complete. Active workers: $active_workers"
    
    # Send notification
    php artisan queue:notification "Worker scaling: Scaled UP for peak hours ($active_workers workers active)"
}

# Scale down workers for off-peak hours
scale_down_offpeak() {
    log "Scaling DOWN for off-peak hours..."
    
    # Gracefully stop peak workers (let them finish current jobs)
    supervisorctl stop laravel-workers-peak:*
    
    # Wait for jobs to complete (max 5 minutes)
    local wait_time=0
    while [[ $wait_time -lt 300 ]]; do
        local running_jobs=$(supervisorctl status laravel-workers-peak:* | grep RUNNING | wc -l)
        if [[ $running_jobs -eq 0 ]]; then
            break
        fi
        sleep 10
        wait_time=$((wait_time + 10))
    done
    
    # Start off-peak workers
    supervisorctl start laravel-workers-offpeak:*
    
    # Wait for workers to start
    sleep 10
    
    # Verify workers are running
    local active_workers=$(supervisorctl status laravel-workers-offpeak:* | grep RUNNING | wc -l)
    log "Off-peak scaling complete. Active workers: $active_workers"
    
    # Send notification
    php artisan queue:notification "Worker scaling: Scaled DOWN for off-peak hours ($active_workers workers active)"
}

# Emergency scaling based on queue size
emergency_scale() {
    local queue_load=$1
    log "EMERGENCY SCALING: Queue load is $queue_load jobs"
    
    # Start additional emergency workers
    supervisorctl start laravel-worker-emergency:* 2>/dev/null || true
    
    # Send alert
    php artisan queue:notification "EMERGENCY: High queue load detected ($queue_load jobs). Additional workers started."
}

# Dynamic scaling based on queue load
dynamic_scale() {
    local queue_load=$(get_total_queue_load)
    log "Current queue load: $queue_load jobs"
    
    # Emergency scaling threshold
    if [[ $queue_load -gt 500 ]]; then
        emergency_scale "$queue_load"
        return
    fi
    
    # Normal time-based scaling
    if is_peak_hours; then
        # Check if peak workers are already running
        local peak_workers=$(supervisorctl status laravel-workers-peak:* 2>/dev/null | grep RUNNING | wc -l)
        if [[ $peak_workers -eq 0 ]]; then
            scale_up_peak
        else
            log "Peak hours detected, peak workers already running ($peak_workers active)"
        fi
    else
        # Check if off-peak workers are running
        local offpeak_workers=$(supervisorctl status laravel-workers-offpeak:* 2>/dev/null | grep RUNNING | wc -l)
        if [[ $offpeak_workers -eq 0 ]]; then
            scale_down_offpeak
        else
            log "Off-peak hours, off-peak workers already running ($offpeak_workers active)"
        fi
    fi
}

# Health check function
health_check() {
    log "Running worker health check..."
    
    # Check Redis connection
    if ! redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping > /dev/null 2>&1; then
        log "ERROR: Redis connection failed"
        php artisan queue:notification "ALERT: Redis connection failed during worker scaling"
        exit 1
    fi
    
    # Check supervisor
    if ! supervisorctl status > /dev/null 2>&1; then
        log "ERROR: Supervisor not available"
        php artisan queue:notification "ALERT: Supervisor not available during worker scaling"
        exit 1
    fi
    
    # Check Laravel application
    if ! php artisan queue:monitor --once > /dev/null 2>&1; then
        log "WARNING: Laravel queue monitor failed"
    fi
    
    log "Health check passed"
}

# Main execution
main() {
    log "Starting worker scaling process..."
    log "Current time: $(date), Peak hours: ${PEAK_HOURS_START}:00-${PEAK_HOURS_END}:00"
    
    # Run health check
    health_check
    
    # Perform dynamic scaling
    dynamic_scale
    
    # Log final status
    local total_workers=$(supervisorctl status | grep laravel-worker | grep RUNNING | wc -l)
    log "Scaling complete. Total active workers: $total_workers"
    
    # Update metrics
    php artisan queue:metrics --update
}

# Handle script arguments
case "${1:-}" in
    "peak")
        log "Manual peak scaling requested"
        scale_up_peak
        ;;
    "offpeak")
        log "Manual off-peak scaling requested"
        scale_down_offpeak
        ;;
    "status")
        echo "Current worker status:"
        supervisorctl status | grep laravel-worker
        echo "Queue sizes:"
        echo "  High: $(get_queue_size 'high')"
        echo "  Default: $(get_queue_size 'default')"
        echo "  Search: $(get_queue_size 'search')"
        echo "  Bulk: $(get_queue_size 'bulk')"
        echo "  Total load: $(get_total_queue_load)"
        ;;
    "health")
        health_check
        ;;
    *)
        main
        ;;
esac

log "Worker scaling script completed"
