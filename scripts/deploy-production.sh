#!/bin/bash

# Production Deployment Script for SuccessionPlan AI
# Zero-downtime deployment with comprehensive monitoring, testing, and rollback capabilities

set -e

# Configuration
DEPLOY_ENV="production"
APP_ROOT="/var/www/successionplanai"
BACKUP_DIR="/var/backups/successionplanai-production"
SHARED_DIR="$APP_ROOT/shared"
LOG_FILE="$APP_ROOT/storage/logs/production-deployment.log"
MAX_DEPLOYMENT_TIME=2400  # 40 minutes for production
HEALTH_CHECK_TIMEOUT=600  # 10 minutes for production health checks
ROLLBACK_RELEASES=10      # Keep more releases in production
MAINTENANCE_WINDOW=${MAINTENANCE_WINDOW:-false}

# Production Server Configuration
WEB_USER="www-data"
DEPLOY_USER="ubuntu"
PHP_VERSION="8.3"
NODE_VERSION="18"
NOTIFICATION_WEBHOOK=${NOTIFICATION_WEBHOOK:-""}

# Safety Configuration
REQUIRE_CONFIRMATION=${REQUIRE_CONFIRMATION:-true}
BACKUP_BEFORE_DEPLOY=${BACKUP_BEFORE_DEPLOY:-true}
RUN_TESTS_BEFORE_DEPLOY=${RUN_TESTS_BEFORE_DEPLOY:-true}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] ✓${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] ⚠${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ✗${NC} $1" | tee -a "$LOG_FILE"
}

log_critical() {
    echo -e "${PURPLE}[$(date '+%Y-%m-%d %H:%M:%S')] 🚨${NC} $1" | tee -a "$LOG_FILE"
}

# Production safety confirmation
confirm_deployment() {
    if [[ "$REQUIRE_CONFIRMATION" == "true" ]]; then
        echo
        log_warning "=========================================="
        log_warning "PRODUCTION DEPLOYMENT CONFIRMATION"
        log_warning "=========================================="
        log_warning "Environment: PRODUCTION"
        log_warning "This will deploy to the live production server"
        log_warning "Users may experience brief service interruption"
        echo
        
        read -p "Are you sure you want to deploy to PRODUCTION? (yes/no): " -r
        if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
            log "Deployment cancelled by user"
            exit 0
        fi
        
        read -p "Have you tested this deployment on staging? (yes/no): " -r
        if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
            log_error "Please test on staging first"
            exit 1
        fi
        
        log_success "Production deployment confirmed"
    fi
}

# Enhanced error handling with automatic rollback
cleanup_on_error() {
    log_critical "PRODUCTION DEPLOYMENT FAILED! Initiating emergency rollback..."
    
    # Send immediate alert
    send_critical_alert "PRODUCTION DEPLOYMENT FAILURE" "Emergency rollback initiated"
    
    # Stop maintenance mode if it was enabled
    if [[ -f "$APP_ROOT/current/storage/framework/maintenance.php" ]]; then
        cd "$APP_ROOT/current" && php artisan up
    fi
    
    # Rollback to previous release
    if [[ -d "$APP_ROOT/releases" ]]; then
        local previous_release=$(ls -t "$APP_ROOT/releases" | sed -n '2p')
        if [[ -n "$previous_release" ]]; then
            log "Rolling back to previous release: $previous_release"
            
            # Atomic symlink switch
            local temp_link="$APP_ROOT/rollback_tmp_$(date +%s)"
            ln -nfs "$APP_ROOT/releases/$previous_release" "$temp_link"
            mv "$temp_link" "$APP_ROOT/current"
            
            # Restart all services
            restart_services_production
            start_queue_services_production "emergency"
            
            # Verify rollback
            sleep 30
            if verify_application_health; then
                log_success "Emergency rollback completed successfully"
                send_critical_alert "ROLLBACK SUCCESS" "Application restored to previous version: $previous_release"
            else
                log_critical "ROLLBACK FAILED - Manual intervention required immediately"
                send_critical_alert "ROLLBACK FAILED" "Manual intervention required - application may be down"
            fi
        else
            log_critical "No previous release found for rollback - manual intervention required"
            send_critical_alert "NO ROLLBACK AVAILABLE" "Manual intervention required immediately"
        fi
    fi
    
    exit 1
}

trap cleanup_on_error ERR

# Comprehensive pre-deployment checks for production
pre_deployment_checks_production() {
    log "Starting comprehensive pre-deployment checks for PRODUCTION..."
    
    # Basic system checks
    if [[ $(whoami) != "$DEPLOY_USER" ]] && [[ $(whoami) != "root" ]]; then
        log_error "Script must be run as $DEPLOY_USER or root user"
        exit 1
    fi
    
    # Enhanced disk space check (need at least 5GB free for production)
    local available_space=$(df "$APP_ROOT" | awk 'NR==2 {print $4}')
    if [[ $available_space -lt 5242880 ]]; then  # 5GB in KB
        log_error "Insufficient disk space. Need at least 5GB free for production deployment."
        exit 1
    fi
    
    # Memory check
    local available_memory=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    if [[ $available_memory -lt 1024 ]]; then  # 1GB
        log_warning "Low memory available: ${available_memory}MB"
    fi
    
    # CPU load check
    local cpu_load=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    if (( $(echo "$cpu_load > 2.0" | bc -l) )); then
        log_warning "High CPU load detected: $cpu_load"
    fi
    
    # Database connectivity and performance
    log "Checking database performance..."
    local db_response_time=$(cd "$APP_ROOT/current" && timeout 10 php artisan tinker --execute="
        \$start = microtime(true);
        DB::connection()->getPdo();
        DB::select('SELECT 1');
        echo round((microtime(true) - \$start) * 1000, 2);
    " 2>/dev/null || echo "timeout")
    
    if [[ "$db_response_time" == "timeout" ]] || (( $(echo "$db_response_time > 1000" | bc -l) )); then
        log_error "Database response time too slow: ${db_response_time}ms"
        exit 1
    fi
    
    # Redis connectivity and performance
    log "Checking Redis performance..."
    local redis_response_time=$(timeout 5 bash -c 'start=$(date +%s%3N); redis-cli ping >/dev/null; end=$(date +%s%3N); echo $((end-start))' || echo "timeout")
    
    if [[ "$redis_response_time" == "timeout" ]] || [[ $redis_response_time -gt 100 ]]; then
        log_error "Redis response time too slow: ${redis_response_time}ms"
        exit 1
    fi
    
    # Check critical services
    local services=("nginx" "php$PHP_VERSION-fpm" "mysql" "redis-server")
    for service in "${services[@]}"; do
        if ! systemctl is-active --quiet "$service"; then
            log_error "Critical service not running: $service"
            exit 1
        fi
    done
    
    # Check SSL certificate expiry
    if command -v openssl &> /dev/null; then
        local ssl_expiry=$(echo | openssl s_client -servername $(hostname) -connect $(hostname):443 2>/dev/null | openssl x509 -noout -dates | grep 'notAfter' | cut -d= -f2)
        local expiry_epoch=$(date -d "$ssl_expiry" +%s 2>/dev/null || echo 0)
        local current_epoch=$(date +%s)
        local days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
        
        if [[ $days_until_expiry -lt 30 ]]; then
            log_warning "SSL certificate expires in $days_until_expiry days"
        fi
    fi
    
    # Check current application health
    if ! verify_application_health; then
        log_error "Current application is not healthy - cannot proceed with deployment"
        exit 1
    fi
    
    log_success "Production pre-deployment checks completed"
}

# Enhanced backup creation for production
create_production_backup() {
    log "Creating comprehensive production backup..."
    
    local backup_timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_path="$BACKUP_DIR/production_$backup_timestamp"
    
    mkdir -p "$backup_path"
    
    # Database backup with compression
    log "Creating database backup..."
    cd "$APP_ROOT/current"
    php artisan backup:run --only-db --filename="production_database_$backup_timestamp.sql"
    
    # Application files backup (exclude vendor and node_modules)
    log "Creating application files backup..."
    rsync -av --exclude='node_modules' --exclude='vendor' \
          --exclude='storage/logs/*' --exclude='storage/framework/cache/*' \
          "$APP_ROOT/current"/ "$backup_path"/
    
    # Shared files backup
    rsync -av "$SHARED_DIR"/ "$backup_path/shared"/
    
    # Compress backup
    cd "$BACKUP_DIR"
    tar -czf "production_backup_$backup_timestamp.tar.gz" "production_$backup_timestamp"
    rm -rf "production_$backup_timestamp"
    
    # Update latest backup symlink
    ln -sfn "production_backup_$backup_timestamp.tar.gz" "$BACKUP_DIR/latest_production_backup.tar.gz"
    
    # Keep only last 10 production backups
    cd "$BACKUP_DIR"
    ls -t production_backup_*.tar.gz | tail -n +11 | xargs -r rm -f
    
    log_success "Production backup created: production_backup_$backup_timestamp.tar.gz"
}

# Production-specific application verification
verify_application_health() {
    log "Verifying application health..."
    
    cd "$APP_ROOT/current"
    
    # Laravel application check
    if ! timeout 30 php artisan about > /dev/null 2>&1; then
        log_error "Laravel application check failed"
        return 1
    fi
    
    # Database connectivity with query test
    if ! timeout 30 php artisan tinker --execute="
        DB::connection()->getPdo();
        \$users = DB::table('users')->count();
        if (\$users < 1) throw new Exception('No users found');
        echo 'Database check passed';
    " > /dev/null 2>&1; then
        log_error "Database verification failed"
        return 1
    fi
    
    # Queue system verification
    if ! timeout 30 php artisan queue:monitor --once > /dev/null 2>&1; then
        log_error "Queue system verification failed"
        return 1
    fi
    
    # HTTP response check
    local app_url=$(grep "APP_URL=" .env | cut -d'=' -f2 | tr -d '"')
    if ! curl -f -s --max-time 30 "$app_url/health" > /dev/null 2>&1; then
        log_warning "HTTP health check failed - endpoint may not exist"
    fi
    
    log_success "Application health verification passed"
    return 0
}

# Production maintenance mode with custom page
enable_maintenance_mode() {
    log "Enabling maintenance mode..."
    
    cd "$APP_ROOT/current"
    
    # Enable maintenance mode with custom message and retry time
    php artisan down \
        --message="We're performing a quick update to improve your experience. We'll be back shortly!" \
        --retry=60 \
        --secret="$(openssl rand -hex 16)"
    
    log_success "Maintenance mode enabled"
}

# Production queue management
manage_production_queues() {
    local action=$1  # pause, resume, or restart
    
    log "Managing production queues: $action"
    
    cd "$APP_ROOT/current"
    
    case $action in
        "pause")
            # Gracefully pause all queues
            php artisan queue:pause-all
            
            # Wait for current jobs to finish (max 5 minutes)
            local wait_time=0
            while [[ $wait_time -lt 300 ]]; do
                local active_jobs=$(pgrep -f "artisan queue:work" | wc -l)
                if [[ $active_jobs -eq 0 ]]; then
                    break
                fi
                sleep 10
                wait_time=$((wait_time + 10))
                log "Waiting for $active_jobs active jobs to complete..."
            done
            
            # Stop Horizon gracefully
            php artisan horizon:terminate
            
            # Stop supervisor workers
            if command -v supervisorctl &> /dev/null; then
                supervisorctl stop laravel-workers-production:* 2>/dev/null || true
            fi
            ;;
            
        "resume")
            php artisan queue:resume-all
            ;;
            
        "restart")
            start_queue_services_production "deployment"
            ;;
    esac
    
    log_success "Queue management completed: $action"
}

# Enhanced production services restart
restart_services_production() {
    log "Restarting production services..."
    
    # Reload PHP-FPM gracefully
    sudo systemctl reload "php$PHP_VERSION-fpm"
    
    # Test PHP-FPM status
    if ! systemctl is-active --quiet "php$PHP_VERSION-fpm"; then
        log_error "PHP-FPM failed to restart"
        exit 1
    fi
    
    # Reload Nginx gracefully
    sudo nginx -t  # Test configuration first
    sudo systemctl reload nginx
    
    # Test Nginx status
    if ! systemctl is-active --quiet nginx; then
        log_error "Nginx failed to restart"
        exit 1
    fi
    
    # Clear OPcache
    php -r "if(function_exists('opcache_reset')) opcache_reset();"
    
    # Clear system page cache
    sync && echo 3 | sudo tee /proc/sys/vm/drop_caches > /dev/null
    
    log_success "Production services restarted successfully"
}

# Production queue services with monitoring
start_queue_services_production() {
    local mode=${1:-"normal"}
    
    log "Starting production queue services in $mode mode..."
    
    cd "$APP_ROOT/current"
    
    # Determine worker configuration based on time and mode
    local current_hour=$(date +"%H")
    local current_day=$(date +"%u")
    local is_peak_hours=false
    
    if [[ $current_day -le 5 ]] && [[ $current_hour -ge 8 ]] && [[ $current_hour -lt 18 ]]; then
        is_peak_hours=true
    fi
    
    # Start Horizon with production configuration
    nohup php artisan horizon > "$SHARED_DIR/storage/logs/horizon.log" 2>&1 &
    
    # Wait for Horizon to start
    sleep 15
    
    # Verify Horizon is running
    local horizon_status=$(php artisan horizon:status 2>/dev/null || echo "inactive")
    if [[ "$horizon_status" != "running" ]]; then
        log_error "Horizon failed to start"
        return 1
    fi
    
    # Start appropriate supervisor workers
    if command -v supervisorctl &> /dev/null; then
        if [[ "$is_peak_hours" == "true" ]] || [[ "$mode" == "emergency" ]]; then
            supervisorctl start laravel-workers-peak:*
        else
            supervisorctl start laravel-workers-offpeak:*
        fi
        
        # Verify workers started
        local active_workers=$(supervisorctl status | grep laravel-worker | grep RUNNING | wc -l)
        log_success "Started $active_workers production workers"
    fi
    
    # Resume queue processing
    php artisan queue:resume-all
    
    log_success "Production queue services started successfully"
}

# Comprehensive post-deployment testing
run_post_deployment_tests() {
    local release_path=$1
    
    log "Running comprehensive post-deployment tests..."
    
    cd "$release_path"
    
    # Laravel application tests
    if [[ -f "phpunit.xml" ]]; then
        log "Running PHPUnit tests..."
        if ! timeout 600 vendor/bin/phpunit --testsuite=Feature --stop-on-failure; then
            log_error "PHPUnit tests failed"
            return 1
        fi
        log_success "PHPUnit tests passed"
    fi
    
    # Critical functionality tests
    log "Testing critical application functionality..."
    
    # Test database operations
    if ! php artisan tinker --execute="
        \$user = App\\Models\\User::first();
        if (!\$user) throw new Exception('No users found');
        echo 'User test passed';
    " > /dev/null 2>&1; then
        log_error "User model test failed"
        return 1
    fi
    
    # Test queue system
    if ! php artisan queue:monitor --once > /dev/null 2>&1; then
        log_error "Queue system test failed"
        return 1
    fi
    
    # Test API endpoints (if applicable)
    local app_url=$(grep "APP_URL=" .env | cut -d'=' -f2 | tr -d '"')
    if curl -f -s --max-time 30 "$app_url/api/health" > /dev/null 2>&1; then
        log_success "API endpoint test passed"
    else
        log_warning "API endpoint test failed or not available"
    fi
    
    log_success "Post-deployment tests completed"
    return 0
}

# Send critical alerts
send_critical_alert() {
    local title=$1
    local message=$2
    
    # Send notification through Laravel
    cd "$APP_ROOT/current" 2>/dev/null || cd "$APP_ROOT"
    php artisan queue:notification "🚨 PRODUCTION ALERT: $title - $message" --level="critical" 2>/dev/null || true
    
    # Send webhook notification if configured
    if [[ -n "$NOTIFICATION_WEBHOOK" ]]; then
        curl -X POST "$NOTIFICATION_WEBHOOK" \
             -H "Content-Type: application/json" \
             -d "{\"title\":\"$title\",\"message\":\"$message\",\"level\":\"critical\",\"timestamp\":\"$(date -Iseconds)\"}" \
             > /dev/null 2>&1 || true
    fi
    
    # Log critical event
    log_critical "$title: $message"
}

# Main production deployment function
main_production() {
    local start_time=$(date +%s)
    
    log_critical "=========================================="
    log_critical "STARTING PRODUCTION DEPLOYMENT"
    log_critical "=========================================="
    log "Deployment started at: $(date)"
    
    # Safety confirmation
    confirm_deployment
    
    # Comprehensive pre-deployment checks
    pre_deployment_checks_production
    
    # Create backup
    if [[ "$BACKUP_BEFORE_DEPLOY" == "true" ]]; then
        create_production_backup
    fi
    
    # Enable maintenance mode unless in maintenance window
    if [[ "$MAINTENANCE_WINDOW" != "true" ]]; then
        enable_maintenance_mode
    fi
    
    # Pause queues gracefully
    manage_production_queues "pause"
    
    # Setup deployment structure
    setup_deployment_structure
    
    # Create new release
    local release_path=$(create_new_release)
    
    # Install dependencies and build
    install_dependencies "$release_path"
    setup_shared_resources "$release_path"
    build_application "$release_path"
    
    # Run migrations
    run_migrations "$release_path"
    
    # Run comprehensive tests
    if [[ "$RUN_TESTS_BEFORE_DEPLOY" == "true" ]]; then
        if ! run_post_deployment_tests "$release_path"; then
            log_error "Pre-deployment tests failed"
            exit 1
        fi
    fi
    
    # Switch to new release (atomic operation)
    switch_release "$release_path"
    
    # Restart production services
    restart_services_production
    
    # Start queue services
    start_queue_services_production "deployment"
    
    # Disable maintenance mode
    if [[ "$MAINTENANCE_WINDOW" != "true" ]]; then
        cd "$APP_ROOT/current"
        php artisan up
        log_success "Maintenance mode disabled"
    fi
    
    # Final health verification
    sleep 30  # Allow services to stabilize
    if ! verify_application_health; then
        log_error "Post-deployment health check failed"
        exit 1
    fi
    
    # Run post-deployment tests
    if ! run_post_deployment_tests "$APP_ROOT/current"; then
        log_warning "Some post-deployment tests failed - monitoring required"
    fi
    
    # Cleanup old releases
    cleanup_old_releases
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # Send success notification
    send_critical_alert "PRODUCTION DEPLOYMENT SUCCESS" "Deployment completed successfully in ${duration} seconds"
    
    # Show deployment summary
    echo
    log_success "=========================================="
    log_success "PRODUCTION DEPLOYMENT COMPLETED"
    log_success "=========================================="
    log "Environment: $DEPLOY_ENV"
    log "Release: $(basename "$release_path")"
    log "Duration: ${duration} seconds"
    log "Horizon status: $(cd "$APP_ROOT/current" && php artisan horizon:status)"
    log "Active workers: $(supervisorctl status 2>/dev/null | grep laravel-worker | grep RUNNING | wc -l)"
    echo
    
    log_critical "PRODUCTION DEPLOYMENT COMPLETED SUCCESSFULLY"
}

# Handle script arguments
case "${1:-}" in
    "rollback")
        confirm_deployment
        log_critical "Rolling back production deployment..."
        cleanup_on_error
        ;;
    "health")
        cd "$APP_ROOT/current"
        verify_application_health
        ;;
    "status")
        echo "Current release: $(readlink "$APP_ROOT/current" | xargs basename)"
        echo "Available releases:"
        ls -la "$APP_ROOT/releases/" | tail -n 5
        echo "Application status:"
        cd "$APP_ROOT/current"
        echo "  Horizon: $(php artisan horizon:status)"
        echo "  Queue health: $(php artisan queue:monitor --once 2>&1 | head -n 1)"
        echo "  Active workers: $(supervisorctl status 2>/dev/null | grep laravel-worker | grep RUNNING | wc -l)"
        ;;
    "maintenance")
        case "${2:-}" in
            "enable")
                cd "$APP_ROOT/current"
                enable_maintenance_mode
                ;;
            "disable")
                cd "$APP_ROOT/current"
                php artisan up
                log_success "Maintenance mode disabled"
                ;;
            *)
                echo "Usage: $0 maintenance [enable|disable]"
                ;;
        esac
        ;;
    *)
        main_production
        ;;
esac
