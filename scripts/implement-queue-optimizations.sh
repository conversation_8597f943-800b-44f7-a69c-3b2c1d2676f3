#!/bin/bash

# Lara<PERSON> Queue, Redis & Horizon Optimization Implementation Script
# This script implements the priority optimizations identified in the analysis

set -e

echo "🚀 Implementing Laravel Queue, Redis & Horizon Optimizations..."

# Function to print colored output
print_status() {
    echo -e "\033[1;34m[INFO]\033[0m $1"
}

print_success() {
    echo -e "\033[1;32m[SUCCESS]\033[0m $1"
}

print_warning() {
    echo -e "\033[1;33m[WARNING]\033[0m $1"
}

print_error() {
    echo -e "\033[1;31m[ERROR]\033[0m $1"
}

# Check if we're in a Laravel project
if [ ! -f "artisan" ]; then
    print_error "This doesn't appear to be a Laravel project directory"
    exit 1
fi

print_status "Laravel project detected. Proceeding with optimizations..."

# 1. Backup current configuration
print_status "Creating backup of current configurations..."
mkdir -p backups/$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"

cp config/database.php "$BACKUP_DIR/"
cp config/queue.php "$BACKUP_DIR/"
cp config/horizon.php "$BACKUP_DIR/"
cp app/Providers/HorizonServiceProvider.php "$BACKUP_DIR/"
cp app/Console/Kernel.php "$BACKUP_DIR/"

print_success "Backups created in $BACKUP_DIR"

# 2. Install required packages
print_status "Installing required packages..."
if ! php -m | grep -q "redis"; then
    print_warning "PHP Redis extension not found. Please install php-redis extension"
    print_warning "Ubuntu/Debian: sudo apt install php-redis"
    print_warning "macOS: brew install php-redis"
fi

if ! php -m | grep -q "igbinary"; then
    print_warning "PHP igbinary extension not found (optional but recommended)"
    print_warning "Ubuntu/Debian: sudo apt install php-igbinary"
    print_warning "macOS: brew install php-igbinary"
fi

# 3. Update environment variables
print_status "Updating environment configuration..."
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Creating from .env.example..."
    cp .env.example .env
fi

# Add new environment variables if they don't exist
grep -q "REDIS_QUEUE_BLOCK_FOR" .env || echo "REDIS_QUEUE_BLOCK_FOR=5" >> .env
grep -q "REDIS_INTERNAL_BLOCK_FOR" .env || echo "REDIS_INTERNAL_BLOCK_FOR=2" >> .env
grep -q "REDIS_EXTERNAL_BLOCK_FOR" .env || echo "REDIS_EXTERNAL_BLOCK_FOR=10" >> .env
grep -q "REDIS_SUCCESSION_BLOCK_FOR" .env || echo "REDIS_SUCCESSION_BLOCK_FOR=3" >> .env
grep -q "HORIZON_ADMINS" .env || echo "HORIZON_ADMINS=<EMAIL>" >> .env
grep -q "REDIS_CLIENT" .env || echo "REDIS_CLIENT=phpredis" >> .env

print_success "Environment variables updated"

# 4. Clear and cache configurations
print_status "Clearing and caching configurations..."
php artisan config:clear
php artisan config:cache
php artisan route:clear
php artisan route:cache
php artisan view:clear

print_success "Configuration cached"

# 5. Run database migrations (if needed)
print_status "Checking database migrations..."
if php artisan migrate:status | grep -q "pending"; then
    print_warning "Pending migrations found. Running migrations..."
    php artisan migrate --force
else
    print_success "All migrations up to date"
fi

# 6. Test Redis connection
print_status "Testing Redis connection..."
if php artisan tinker --execute="Redis::ping()" 2>/dev/null | grep -q "PONG"; then
    print_success "Redis connection successful"
else
    print_error "Redis connection failed. Please check Redis installation and configuration"
    exit 1
fi

# 7. Install supervisor configurations (if supervisor is available)
if command -v supervisorctl &> /dev/null; then
    print_status "Supervisor detected. Installing configuration files..."
    
    # Determine environment
    APP_ENV=$(grep "APP_ENV" .env | cut -d '=' -f2)
    
    if [ "$APP_ENV" = "production" ]; then
        print_status "Installing production supervisor configuration..."
        sudo cp supervisor/horizon-production.conf /etc/supervisor/conf.d/
        sudo sed -i "s|/var/www/production|$(pwd)|g" /etc/supervisor/conf.d/horizon-production.conf
    elif [ "$APP_ENV" = "staging" ]; then
        print_status "Installing staging supervisor configuration..."
        sudo cp supervisor/horizon-staging.conf /etc/supervisor/conf.d/
        sudo sed -i "s|/var/www/staging|$(pwd)|g" /etc/supervisor/conf.d/horizon-staging.conf
    else
        print_status "Installing local supervisor configuration..."
        sudo cp supervisor/horizon-local.conf /etc/supervisor/conf.d/
        sudo sed -i "s|/path/to/your/project|$(pwd)|g" /etc/supervisor/conf.d/horizon-local.conf
    fi
    
    # Reload supervisor
    sudo supervisorctl reread
    sudo supervisorctl update
    
    print_success "Supervisor configuration installed"
else
    print_warning "Supervisor not found. Please install supervisor to manage Horizon processes"
    print_warning "Ubuntu/Debian: sudo apt install supervisor"
    print_warning "macOS: brew install supervisor"
fi

# 8. Start/Restart Horizon
print_status "Starting Horizon..."
if pgrep -f "artisan horizon" > /dev/null; then
    print_status "Stopping existing Horizon processes..."
    php artisan horizon:terminate
    sleep 5
fi

# Start Horizon in background
nohup php artisan horizon > storage/logs/horizon.log 2>&1 &
sleep 3

# Check if Horizon is running
if php artisan horizon:status | grep -q "running"; then
    print_success "Horizon started successfully"
else
    print_error "Failed to start Horizon. Check logs for details"
    tail -n 20 storage/logs/horizon.log
fi

# 9. Run tests to verify everything is working
print_status "Running verification tests..."

# Test queue configuration
php artisan queue:work --timeout=5 --stop-when-empty > /dev/null 2>&1 || true

# Test Horizon dashboard (if accessible)
if command -v curl &> /dev/null; then
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/horizon | grep -q "200\|302"; then
        print_success "Horizon dashboard accessible"
    else
        print_warning "Horizon dashboard not accessible. Make sure your web server is running"
    fi
fi

# 10. Performance recommendations
print_status "Performance optimization recommendations:"
echo "  1. Consider upgrading to Redis 6+ for better performance"
echo "  2. Monitor memory usage with: redis-cli info memory"
echo "  3. Set up Redis persistence for production: save 900 1 && appendonly yes"
echo "  4. Consider Redis clustering for high-availability production setups"
echo "  5. Monitor queue performance with: php artisan horizon:status"

# 11. Next steps
print_success "🎉 Queue optimizations implemented successfully!"
echo ""
print_status "Next steps:"
echo "  1. Update HORIZON_ADMINS in .env with actual admin emails"
echo "  2. Test queue processing: php artisan queue:work"
echo "  3. Access Horizon dashboard at: /horizon"
echo "  4. Monitor queue performance and adjust settings as needed"
echo "  5. Set up monitoring alerts for production environments"
echo ""
print_status "Useful commands:"
echo "  - Check Horizon status: php artisan horizon:status"
echo "  - Monitor queues: php artisan queue:monitor redis:default"
echo "  - View failed jobs: php artisan queue:failed"
echo "  - Restart queue workers: php artisan queue:restart"
echo "  - Clear failed jobs: php artisan queue:flush --failed"
echo ""
print_success "Implementation complete! 🚀"
