#!/bin/bash

# Staging Deployment Script for SuccessionPlan AI
# Zero-downtime deployment with comprehensive testing and rollback capabilities

set -e

# Configuration
DEPLOY_ENV="staging"
APP_ROOT="/var/www/successionplanai"
BACKUP_DIR="/var/backups/successionplanai-staging"
SHARED_DIR="$APP_ROOT/shared"
LOG_FILE="$APP_ROOT/storage/logs/staging-deployment.log"
MAX_DEPLOYMENT_TIME=1800  # 30 minutes
HEALTH_CHECK_TIMEOUT=300  # 5 minutes
ROLLBACK_RELEASES=5       # Keep last 5 releases

# Server Configuration
WEB_USER="www-data"
DEPLOY_USER="ubuntu"
PHP_VERSION="8.3"
NODE_VERSION="18"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] ✓${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] ⚠${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ✗${NC} $1" | tee -a "$LOG_FILE"
}

# Error handling and rollback
cleanup_on_error() {
    log_error "Deployment failed! Starting rollback procedure..."
    
    # Rollback to previous release
    if [[ -d "$APP_ROOT/releases" ]]; then
        local previous_release=$(ls -t "$APP_ROOT/releases" | sed -n '2p')
        if [[ -n "$previous_release" ]]; then
            log "Rolling back to previous release: $previous_release"
            
            # Update symlink to previous release
            ln -nfs "$APP_ROOT/releases/$previous_release" "$APP_ROOT/current"
            
            # Restart services
            restart_services
            
            # Send notification
            send_deployment_notification "ROLLBACK" "Deployment failed and rolled back to $previous_release"
        fi
    fi
    
    # Restart with off-peak configuration
    start_queue_services "rollback"
    
    exit 1
}

trap cleanup_on_error ERR

# Pre-deployment checks
pre_deployment_checks() {
    log "Starting pre-deployment checks for staging environment..."
    
    # Check if we're running as the correct user
    if [[ $(whoami) != "$DEPLOY_USER" ]] && [[ $(whoami) != "root" ]]; then
        log_error "Script must be run as $DEPLOY_USER or root user"
        exit 1
    fi
    
    # Check disk space (need at least 3GB free for staging)
    local available_space=$(df "$APP_ROOT" | awk 'NR==2 {print $4}')
    if [[ $available_space -lt 3145728 ]]; then  # 3GB in KB
        log_error "Insufficient disk space. Need at least 3GB free."
        exit 1
    fi
    
    # Check database connection
    if ! php artisan tinker --execute="DB::connection()->getPdo();" > /dev/null 2>&1; then
        log_error "Database connection failed"
        exit 1
    fi
    
    # Check Redis connection
    if ! redis-cli ping > /dev/null 2>&1; then
        log_error "Redis connection failed"
        exit 1
    fi
    
    # Check web server
    if ! systemctl is-active --quiet nginx; then
        log_error "Nginx is not running"
        exit 1
    fi
    
    # Check PHP-FPM
    if ! systemctl is-active --quiet "php$PHP_VERSION-fpm"; then
        log_error "PHP-FPM is not running"
        exit 1
    fi
    
    log_success "Pre-deployment checks completed"
}

# Create deployment structure
setup_deployment_structure() {
    log "Setting up deployment structure..."
    
    # Create necessary directories
    sudo mkdir -p "$APP_ROOT"/{releases,shared}
    sudo mkdir -p "$SHARED_DIR"/{storage,uploads}
    sudo mkdir -p "$BACKUP_DIR"
    
    # Set ownership
    sudo chown -R "$DEPLOY_USER:$WEB_USER" "$APP_ROOT"
    sudo chown -R "$WEB_USER:$WEB_USER" "$SHARED_DIR"
    
    # Set permissions
    sudo chmod -R 755 "$APP_ROOT"
    sudo chmod -R 775 "$SHARED_DIR"
    
    log_success "Deployment structure ready"
}

# Create new release
create_new_release() {
    local release_timestamp=$(date +"%Y%m%d_%H%M%S")
    local release_path="$APP_ROOT/releases/$release_timestamp"
    
    log "Creating new release: $release_timestamp"
    
    # Clone repository to new release directory
    git clone --depth 1 --branch staging https://github.com/SuccessionplanAI/successionplan-ai.git "$release_path"
    
    cd "$release_path"
    
    # Set ownership
    sudo chown -R "$DEPLOY_USER:$WEB_USER" "$release_path"
    
    echo "$release_path"
}

# Install dependencies
install_dependencies() {
    local release_path=$1
    
    log "Installing dependencies in $release_path..."
    
    cd "$release_path"
    
    # Install Composer dependencies
    log "Installing Composer dependencies..."
    composer install --no-interaction --prefer-dist --no-dev --optimize-autoloader
    
    # Install Node.js dependencies
    log "Installing Node.js dependencies..."
    npm install --legacy-peer-deps --production
    
    # Install additional packages as per current deployment
    npm install @inertiajs/inertia sweetalert2 react-window
    
    # Install additional Composer packages
    composer require rap2hpoutre/fast-excel phpoffice/phpword --no-interaction
    
    log_success "Dependencies installed"
}

# Setup shared resources
setup_shared_resources() {
    local release_path=$1
    
    log "Setting up shared resources..."
    
    cd "$release_path"
    
    # Link shared storage
    rm -rf storage
    ln -nfs "$SHARED_DIR/storage" storage
    
    # Link .env file
    if [[ -f "$SHARED_DIR/.env" ]]; then
        rm -f .env
        ln -nfs "$SHARED_DIR/.env" .env
    else
        log_error "Staging .env file not found in shared directory"
        exit 1
    fi
    
    # Ensure shared directories exist and have correct permissions
    sudo mkdir -p "$SHARED_DIR/storage"/{app,framework,logs}
    sudo mkdir -p "$SHARED_DIR/storage/framework"/{cache,sessions,views}
    sudo mkdir -p "$SHARED_DIR/storage/app"/{public,uploads}
    
    # Set permissions
    sudo chown -R "$WEB_USER:$WEB_USER" "$SHARED_DIR/storage"
    sudo chmod -R 775 "$SHARED_DIR/storage"
    
    # Create storage link
    if [[ ! -L "public/storage" ]]; then
        php artisan storage:link
    fi
    
    log_success "Shared resources configured"
}

# Build application
build_application() {
    local release_path=$1
    
    log "Building application..."
    
    cd "$release_path"
    
    # Generate application key if needed
    if ! grep -q "APP_KEY=base64:" .env; then
        php artisan key:generate --force
    fi
    
    # Build frontend assets
    log "Building frontend assets..."
    npm run build
    
    # Cache Laravel configuration
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache
    
    # Set final permissions
    sudo chown -R "$DEPLOY_USER:$WEB_USER" "$release_path"
    sudo chmod -R 755 "$release_path"
    sudo chmod -R 775 "$release_path/storage" "$release_path/bootstrap/cache"
    
    log_success "Application built successfully"
}

# Run database migrations
run_migrations() {
    local release_path=$1
    
    log "Running database migrations..."
    
    cd "$release_path"
    
    # Create backup before migrations
    php artisan backup:run --only-db --filename="staging_pre_migration_$(date +%Y%m%d_%H%M%S).sql" || log_warning "Backup failed, continuing with migration"
    
    # Run migrations
    php artisan migrate --force
    
    log_success "Database migrations completed"
}

# Health checks
run_health_checks() {
    local release_path=$1
    
    log "Running health checks..."
    
    cd "$release_path"
    
    # Check Laravel application
    if ! php artisan about > /dev/null 2>&1; then
        log_error "Laravel application health check failed"
        return 1
    fi
    
    # Check database connectivity
    if ! php artisan tinker --execute="DB::connection()->getPdo();" > /dev/null 2>&1; then
        log_error "Database connectivity check failed"
        return 1
    fi
    
    # Check queue system
    if ! php artisan queue:monitor --once > /dev/null 2>&1; then
        log_warning "Queue system check failed - may need manual intervention"
    fi
    
    # Run automated tests if available
    if [[ -f "phpunit.xml" ]]; then
        log "Running automated tests..."
        if ! vendor/bin/phpunit --testsuite=Feature --stop-on-failure; then
            log_error "Automated tests failed"
            return 1
        fi
        log_success "Automated tests passed"
    fi
    
    log_success "Health checks completed"
    return 0
}

# Switch to new release
switch_release() {
    local release_path=$1
    
    log "Switching to new release..."
    
    # Update symlink atomically
    local temp_link="$APP_ROOT/current_tmp_$(date +%s)"
    ln -nfs "$release_path" "$temp_link"
    mv "$temp_link" "$APP_ROOT/current"
    
    log_success "Switched to new release"
}

# Restart services
restart_services() {
    log "Restarting services..."
    
    # Restart PHP-FPM
    sudo systemctl reload "php$PHP_VERSION-fpm"
    
    # Restart Nginx
    sudo systemctl reload nginx
    
    # Clear OPcache
    if command -v php &> /dev/null; then
        php -r "if(function_exists('opcache_reset')) opcache_reset();"
    fi
    
    log_success "Services restarted"
}

# Start queue services for staging
start_queue_services() {
    local mode=${1:-"normal"}
    
    log "Starting queue services in $mode mode..."
    
    cd "$APP_ROOT/current"
    
    # Stop existing Horizon
    php artisan horizon:terminate || true
    
    # Wait for graceful shutdown
    sleep 5
    
    # Start Horizon with staging configuration
    nohup php artisan horizon > "$SHARED_DIR/storage/logs/horizon.log" 2>&1 &
    
    # Wait and verify
    sleep 10
    local horizon_status=$(php artisan horizon:status 2>/dev/null || echo "inactive")
    
    if [[ "$horizon_status" == "running" ]]; then
        log_success "Horizon started successfully"
    else
        log_warning "Horizon may not have started correctly"
    fi
    
    # Start staging workers (lighter load than production)
    if command -v supervisorctl &> /dev/null; then
        supervisorctl start laravel-workers-staging:* 2>/dev/null || log_warning "Supervisor not configured for staging"
    fi
}

# Cleanup old releases
cleanup_old_releases() {
    log "Cleaning up old releases..."
    
    cd "$APP_ROOT/releases"
    
    # Keep only the last N releases
    local releases_to_remove=$(ls -t | tail -n +$((ROLLBACK_RELEASES + 1)))
    
    if [[ -n "$releases_to_remove" ]]; then
        echo "$releases_to_remove" | xargs rm -rf
        log_success "Removed old releases: $releases_to_remove"
    else
        log "No old releases to remove"
    fi
}

# Send deployment notification
send_deployment_notification() {
    local status=$1
    local message=${2:-"Staging deployment $status"}
    
    cd "$APP_ROOT/current"
    
    # Send Laravel notification
    php artisan queue:notification "STAGING DEPLOYMENT $status: $message" --level="info"
    
    # Log deployment event
    log "$message"
}

# Main deployment function
main() {
    local start_time=$(date +%s)
    
    log "Starting SuccessionPlan AI Staging Deployment"
    log "Deployment started at: $(date)"
    
    # Setup deployment environment
    setup_deployment_structure
    
    # Run pre-deployment checks
    pre_deployment_checks
    
    # Create new release
    local release_path=$(create_new_release)
    
    # Install dependencies and build
    install_dependencies "$release_path"
    setup_shared_resources "$release_path"
    build_application "$release_path"
    
    # Run migrations
    run_migrations "$release_path"
    
    # Health checks
    if ! run_health_checks "$release_path"; then
        log_error "Health checks failed"
        exit 1
    fi
    
    # Switch to new release
    switch_release "$release_path"
    
    # Restart services
    restart_services
    
    # Start queue services
    start_queue_services
    
    # Cleanup
    cleanup_old_releases
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # Send success notification
    send_deployment_notification "SUCCESS" "Staging deployment completed in ${duration} seconds"
    
    # Show deployment summary
    echo
    log_success "=== STAGING DEPLOYMENT COMPLETED ==="
    log "Environment: $DEPLOY_ENV"
    log "Release: $(basename "$release_path")"
    log "Duration: ${duration} seconds"
    log "Horizon status: $(cd "$APP_ROOT/current" && php artisan horizon:status)"
    echo
    
    log_success "Staging deployment completed successfully"
}

# Handle script arguments
case "${1:-}" in
    "rollback")
        log "Rolling back to previous release..."
        cleanup_on_error
        ;;
    "health")
        cd "$APP_ROOT/current"
        run_health_checks "$APP_ROOT/current"
        ;;
    "status")
        echo "Current release: $(readlink "$APP_ROOT/current" | xargs basename)"
        echo "Available releases:"
        ls -la "$APP_ROOT/releases/"
        echo "Horizon status: $(cd "$APP_ROOT/current" && php artisan horizon:status)"
        ;;
    *)
        main
        ;;
esac
