#!/bin/bash

# Queue Health Monitoring Script for SuccessionPlan AI
# Monitors queue health, sends alerts, and performs automatic recovery

set -e

# Configuration
REDIS_HOST=${REDIS_HOST:-127.0.0.1}
REDIS_PORT=${REDIS_PORT:-6379}
LOG_FILE="/var/www/html/storage/logs/queue-health.log"
ALERT_THRESHOLDS_HIGH=100
ALERT_THRESHOLDS_CRITICAL=500
WORKER_RESTART_THRESHOLD=5
MAX_QUEUE_WAIT_TIME=3600  # 1 hour max wait time

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Get queue size
get_queue_size() {
    local queue_name=$1
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" llen "queues:$queue_name" 2>/dev/null || echo 0
}

# Get failed jobs count
get_failed_jobs_count() {
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" llen "queues:failed" 2>/dev/null || echo 0
}

# Check worker health
check_worker_health() {
    local running_workers=$(supervisorctl status | grep laravel-worker | grep RUNNING | wc -l)
    local total_workers=$(supervisorctl status | grep laravel-worker | wc -l)
    
    echo "$running_workers/$total_workers"
}

# Check Redis health
check_redis_health() {
    if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping > /dev/null 2>&1; then
        echo "healthy"
    else
        echo "unhealthy"
    fi
}

# Check Horizon health
check_horizon_health() {
    local horizon_status=$(php artisan horizon:status 2>/dev/null || echo "inactive")
    echo "$horizon_status"
}

# Get queue processing rates
get_processing_rates() {
    local processed_1min=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" get "queue_stats:processed_1min" 2>/dev/null || echo 0)
    local processed_5min=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" get "queue_stats:processed_5min" 2>/dev/null || echo 0)
    local processed_15min=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" get "queue_stats:processed_15min" 2>/dev/null || echo 0)
    
    echo "1min:$processed_1min 5min:$processed_5min 15min:$processed_15min"
}

# Check for stuck jobs
check_stuck_jobs() {
    local stuck_jobs=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" eval "
        local stuck = 0
        local now = ARGV[1]
        local threshold = ARGV[2]
        
        local processing_jobs = redis.call('SMEMBERS', 'jobs:processing')
        for i, job_id in ipairs(processing_jobs) do
            local start_time = redis.call('HGET', 'job:' .. job_id, 'started_at')
            if start_time and (now - start_time) > threshold then
                stuck = stuck + 1
            end
        end
        
        return stuck
    " 0 $(date +%s) $MAX_QUEUE_WAIT_TIME 2>/dev/null || echo 0)
    
    echo "$stuck_jobs"
}

# Send alert
send_alert() {
    local level=$1
    local message=$2
    
    log "ALERT [$level]: $message"
    
    # Send notification through Laravel
    php artisan queue:notification "[$level] Queue Health Alert: $message" --level="$level"
    
    # Log to Redis for monitoring dashboard
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" lpush "queue_alerts" "$(date '+%Y-%m-%d %H:%M:%S')|$level|$message" > /dev/null 2>&1 || true
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ltrim "queue_alerts" 0 999 > /dev/null 2>&1 || true
}

# Restart workers
restart_workers() {
    local reason=$1
    
    log "Restarting workers due to: $reason"
    
    # Graceful restart
    supervisorctl restart laravel-workers-offpeak:* laravel-workers-peak:* 2>/dev/null || true
    
    # Wait for workers to start
    sleep 10
    
    # Verify restart
    local running_workers=$(supervisorctl status | grep laravel-worker | grep RUNNING | wc -l)
    
    if [[ $running_workers -gt 0 ]]; then
        log "Workers restarted successfully. $running_workers workers now running."
        send_alert "INFO" "Queue workers restarted successfully ($running_workers active)"
    else
        log "ERROR: Worker restart failed"
        send_alert "CRITICAL" "Queue worker restart FAILED - manual intervention required"
    fi
}

# Restart Horizon
restart_horizon() {
    local reason=$1
    
    log "Restarting Horizon due to: $reason"
    
    # Terminate Horizon
    php artisan horizon:terminate
    
    # Wait a moment
    sleep 5
    
    # Start Horizon
    php artisan horizon &
    
    # Wait and check status
    sleep 10
    local horizon_status=$(php artisan horizon:status 2>/dev/null || echo "inactive")
    
    if [[ "$horizon_status" == "running" ]]; then
        log "Horizon restarted successfully"
        send_alert "INFO" "Horizon restarted successfully"
    else
        log "ERROR: Horizon restart failed"
        send_alert "CRITICAL" "Horizon restart FAILED - manual intervention required"
    fi
}

# Auto-recovery for failed jobs
recover_failed_jobs() {
    local failed_count=$(get_failed_jobs_count)
    
    if [[ $failed_count -gt 10 ]]; then
        log "Attempting to retry $failed_count failed jobs"
        
        # Retry failed jobs (limit to prevent overwhelming the system)
        php artisan queue:retry --range=1-50
        
        send_alert "INFO" "Auto-recovery: Retried up to 50 failed jobs (total failed: $failed_count)"
    fi
}

# Clear old job data
cleanup_old_data() {
    log "Cleaning up old queue data..."
    
    # Use Redis advanced service for cleanup
    php artisan tinker --execute="app('App\Services\RedisAdvancedService')->cleanupExpiredKeys();"
    
    # Clean up old logs (keep last 7 days)
    find /var/www/html/storage/logs/ -name "*.log" -mtime +7 -delete 2>/dev/null || true
    
    log "Cleanup completed"
}

# Generate health report
generate_health_report() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Collect all metrics
    local high_queue=$(get_queue_size "high")
    local default_queue=$(get_queue_size "default")
    local search_queue=$(get_queue_size "search")
    local bulk_queue=$(get_queue_size "bulk")
    local failed_jobs=$(get_failed_jobs_count)
    local worker_health=$(check_worker_health)
    local redis_health=$(check_redis_health)
    local horizon_health=$(check_horizon_health)
    local processing_rates=$(get_processing_rates)
    local stuck_jobs=$(check_stuck_jobs)
    
    # Calculate total queue size
    local total_queued=$((high_queue + default_queue + search_queue + bulk_queue))
    
    # Store metrics in Redis for monitoring dashboard
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" hmset "queue_health:latest" \
        "timestamp" "$timestamp" \
        "high_queue" "$high_queue" \
        "default_queue" "$default_queue" \
        "search_queue" "$search_queue" \
        "bulk_queue" "$bulk_queue" \
        "total_queued" "$total_queued" \
        "failed_jobs" "$failed_jobs" \
        "worker_health" "$worker_health" \
        "redis_health" "$redis_health" \
        "horizon_health" "$horizon_health" \
        "processing_rates" "$processing_rates" \
        "stuck_jobs" "$stuck_jobs" \
        > /dev/null 2>&1 || true
    
    # Set expiration
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" expire "queue_health:latest" 3600 > /dev/null 2>&1 || true
    
    # Add to time series
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" zadd "queue_health:history" $(date +%s) "$timestamp|$total_queued|$failed_jobs|$worker_health" > /dev/null 2>&1 || true
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" zremrangebyrank "queue_health:history" 0 -1441 > /dev/null 2>&1 || true  # Keep 24 hours of data
    
    echo "Health Report ($timestamp):"
    echo "  Queue Sizes: High=$high_queue, Default=$default_queue, Search=$search_queue, Bulk=$bulk_queue"
    echo "  Total Queued: $total_queued"
    echo "  Failed Jobs: $failed_jobs"
    echo "  Stuck Jobs: $stuck_jobs"
    echo "  Worker Health: $worker_health"
    echo "  Redis Health: $redis_health"
    echo "  Horizon Health: $horizon_health"
    echo "  Processing Rates: $processing_rates"
}

# Main monitoring function
main_monitor() {
    log "Starting queue health monitoring cycle"
    
    # Generate health report
    generate_health_report
    
    # Get current metrics
    local high_queue=$(get_queue_size "high")
    local default_queue=$(get_queue_size "default")
    local search_queue=$(get_queue_size "search")
    local bulk_queue=$(get_queue_size "bulk")
    local total_queued=$((high_queue + default_queue + search_queue + bulk_queue))
    local failed_jobs=$(get_failed_jobs_count)
    local stuck_jobs=$(check_stuck_jobs)
    local redis_health=$(check_redis_health)
    local horizon_health=$(check_horizon_health)
    local worker_health=$(check_worker_health)
    
    # Check for critical issues
    local issues_found=0
    
    # Check Redis health
    if [[ "$redis_health" != "healthy" ]]; then
        send_alert "CRITICAL" "Redis is unhealthy - queue processing may be affected"
        issues_found=$((issues_found + 1))
    fi
    
    # Check Horizon health
    if [[ "$horizon_health" != "running" ]]; then
        send_alert "WARNING" "Horizon is not running - attempting restart"
        restart_horizon "Horizon not running"
        issues_found=$((issues_found + 1))
    fi
    
    # Check worker health
    local running_workers=$(echo "$worker_health" | cut -d'/' -f1)
    local total_workers=$(echo "$worker_health" | cut -d'/' -f2)
    
    if [[ $running_workers -lt $((total_workers / 2)) ]]; then
        send_alert "CRITICAL" "Less than 50% of workers are running ($worker_health)"
        restart_workers "Too few workers running"
        issues_found=$((issues_found + 1))
    fi
    
    # Check queue sizes
    if [[ $total_queued -gt $ALERT_THRESHOLDS_CRITICAL ]]; then
        send_alert "CRITICAL" "Queue size is critical: $total_queued jobs"
        # Emergency scaling
        /var/www/html/scripts/scale-workers.sh peak
        issues_found=$((issues_found + 1))
    elif [[ $total_queued -gt $ALERT_THRESHOLDS_HIGH ]]; then
        send_alert "WARNING" "Queue size is high: $total_queued jobs"
    fi
    
    # Check failed jobs
    if [[ $failed_jobs -gt 50 ]]; then
        send_alert "WARNING" "High number of failed jobs: $failed_jobs"
        recover_failed_jobs
    fi
    
    # Check for stuck jobs
    if [[ $stuck_jobs -gt 0 ]]; then
        send_alert "WARNING" "Stuck jobs detected: $stuck_jobs jobs running longer than $MAX_QUEUE_WAIT_TIME seconds"
        issues_found=$((issues_found + 1))
    fi
    
    # Periodic cleanup (every 6th run, approximately every hour if run every 10 minutes)
    if [[ $(($(date +%s) % 3600)) -lt 600 ]]; then
        cleanup_old_data
    fi
    
    if [[ $issues_found -eq 0 ]]; then
        log "Health check completed - no issues found"
    else
        log "Health check completed - $issues_found issues found and addressed"
    fi
}

# Handle script arguments
case "${1:-}" in
    "report")
        generate_health_report
        ;;
    "restart-workers")
        restart_workers "Manual restart requested"
        ;;
    "restart-horizon")
        restart_horizon "Manual restart requested"
        ;;
    "cleanup")
        cleanup_old_data
        ;;
    "recover")
        recover_failed_jobs
        ;;
    *)
        main_monitor
        ;;
esac
