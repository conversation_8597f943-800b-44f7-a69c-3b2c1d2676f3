#!/bin/bash

# Advanced deployment script with comprehensive queue management
# For SuccessionPlan AI - Production deployment with zero-downtime queue handling

set -e

# Configuration
DEPLOY_ENV=${DEPLOY_ENV:-production}
APP_ROOT=${APP_ROOT:-/var/www/html}
BACKUP_DIR="/var/backups/successionplan"
LOG_FILE="$APP_ROOT/storage/logs/deployment.log"
MAX_DEPLOYMENT_TIME=1800  # 30 minutes maximum deployment time
HEALTH_CHECK_TIMEOUT=300  # 5 minutes for health checks

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] ✓${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] ⚠${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ✗${NC} $1" | tee -a "$LOG_FILE"
}

# Error handling
cleanup_on_error() {
    log_error "Deployment failed! Starting rollback procedure..."
    
    # Stop new workers
    supervisorctl stop laravel-workers-peak:* laravel-workers-offpeak:* 2>/dev/null || true
    
    # Restore from backup if exists
    if [[ -f "$BACKUP_DIR/latest/composer.json" ]]; then
        log "Restoring from backup..."
        cp -r "$BACKUP_DIR/latest/"* "$APP_ROOT/"
        cd "$APP_ROOT"
        composer install --no-dev --optimize-autoloader
        php artisan config:cache
        php artisan route:cache
        php artisan view:cache
    fi
    
    # Restart workers with old configuration
    supervisorctl start laravel-workers-offpeak:*
    
    # Send failure notification
    php artisan queue:notification "DEPLOYMENT FAILED: Automatic rollback completed"
    
    exit 1
}

trap cleanup_on_error ERR

# Pre-deployment checks
pre_deployment_checks() {
    log "Starting pre-deployment checks..."
    
    # Check if we're running as the correct user
    if [[ $(whoami) != "www-data" ]] && [[ $(whoami) != "root" ]]; then
        log_error "Script must be run as www-data or root user"
        exit 1
    fi
    
    # Check disk space (need at least 2GB free)
    local available_space=$(df "$APP_ROOT" | awk 'NR==2 {print $4}')
    if [[ $available_space -lt 2097152 ]]; then  # 2GB in KB
        log_error "Insufficient disk space. Need at least 2GB free."
        exit 1
    fi
    
    # Check Redis connection
    if ! redis-cli ping > /dev/null 2>&1; then
        log_error "Redis connection failed"
        exit 1
    fi
    
    # Check database connection
    if ! php artisan tinker --execute="DB::connection()->getPdo();" > /dev/null 2>&1; then
        log_error "Database connection failed"
        exit 1
    fi
    
    # Check Horizon status
    local horizon_status=$(php artisan horizon:status 2>/dev/null || echo "inactive")
    if [[ "$horizon_status" == "inactive" ]]; then
        log_warning "Horizon is not running - will be started after deployment"
    fi
    
    log_success "Pre-deployment checks completed"
}

# Create backup
create_backup() {
    log "Creating backup..."
    
    local backup_timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_path="$BACKUP_DIR/$backup_timestamp"
    
    mkdir -p "$backup_path"
    
    # Backup application files
    rsync -av --exclude='node_modules' --exclude='storage/logs/*' \
          --exclude='storage/framework/cache/*' --exclude='vendor' \
          "$APP_ROOT"/ "$backup_path"/
    
    # Backup database
    php artisan backup:run --only-db --filename="database_$backup_timestamp.sql"
    
    # Create symlink to latest backup
    ln -sfn "$backup_path" "$BACKUP_DIR/latest"
    
    # Keep only last 5 backups
    cd "$BACKUP_DIR"
    ls -t | tail -n +6 | xargs -r rm -rf
    
    log_success "Backup created at $backup_path"
}

# Graceful queue shutdown
graceful_queue_shutdown() {
    log "Starting graceful queue shutdown..."
    
    # Get current queue sizes
    local high_queue=$(redis-cli llen "queues:high" 2>/dev/null || echo 0)
    local default_queue=$(redis-cli llen "queues:default" 2>/dev/null || echo 0)
    local search_queue=$(redis-cli llen "queues:search" 2>/dev/null || echo 0)
    
    log "Current queue sizes - High: $high_queue, Default: $default_queue, Search: $search_queue"
    
    # Pause queue processing (prevent new jobs from being processed)
    php artisan queue:pause-all
    
    # Wait for current jobs to finish (max 10 minutes)
    local wait_time=0
    local max_wait=600
    
    while [[ $wait_time -lt $max_wait ]]; do
        local active_jobs=$(supervisorctl status | grep laravel-worker | grep RUNNING | wc -l)
        
        if [[ $active_jobs -eq 0 ]]; then
            log_success "All workers have finished their jobs"
            break
        fi
        
        log "Waiting for $active_jobs workers to finish... (${wait_time}s elapsed)"
        sleep 10
        wait_time=$((wait_time + 10))
    done
    
    # Force stop any remaining workers
    supervisorctl stop laravel-workers-peak:* laravel-workers-offpeak:* 2>/dev/null || true
    
    # Stop Horizon
    php artisan horizon:terminate
    
    log_success "Queue shutdown completed"
}

# Deploy application code
deploy_application() {
    log "Deploying application code..."
    
    cd "$APP_ROOT"
    
    # Pull latest code (assuming git deployment)
    if [[ -d ".git" ]]; then
        git fetch origin
        git reset --hard origin/main
        log_success "Code updated from Git"
    fi
    
    # Install/update Composer dependencies
    log "Installing Composer dependencies..."
    composer install --no-dev --optimize-autoloader --no-interaction
    
    # Install/update NPM dependencies and build assets
    log "Building frontend assets..."
    npm ci --production
    npm run build
    
    # Run database migrations
    log "Running database migrations..."
    php artisan migrate --force
    
    # Clear and rebuild caches
    log "Rebuilding application caches..."
    php artisan config:clear
    php artisan route:clear
    php artisan view:clear
    php artisan cache:clear
    
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache
    
    # Update storage permissions
    chown -R www-data:www-data storage/ bootstrap/cache/
    chmod -R 775 storage/ bootstrap/cache/
    
    log_success "Application deployment completed"
}

# Update queue configurations
update_queue_configurations() {
    log "Updating queue configurations..."
    
    # Copy new supervisor configurations
    cp supervisor/*.conf /etc/supervisor/conf.d/
    
    # Reload supervisor configuration
    supervisorctl reread
    supervisorctl update
    
    # Update cron jobs for scaling
    (crontab -l 2>/dev/null | grep -v "scale-workers.sh"; echo "*/5 * * * * $APP_ROOT/scripts/scale-workers.sh >> $APP_ROOT/storage/logs/cron.log 2>&1") | crontab -
    
    log_success "Queue configurations updated"
}

# Health checks
health_checks() {
    log "Running post-deployment health checks..."
    
    local start_time=$(date +%s)
    local max_wait=$HEALTH_CHECK_TIMEOUT
    
    # Check application health
    while [[ $(($(date +%s) - start_time)) -lt $max_wait ]]; do
        if curl -f -s "$APP_URL/health" > /dev/null 2>&1; then
            log_success "Application health check passed"
            break
        fi
        sleep 5
    done
    
    # Check database connectivity
    if php artisan tinker --execute="DB::connection()->getPdo();" > /dev/null 2>&1; then
        log_success "Database connectivity check passed"
    else
        log_error "Database connectivity check failed"
        return 1
    fi
    
    # Check Redis connectivity
    if redis-cli ping > /dev/null 2>&1; then
        log_success "Redis connectivity check passed"
    else
        log_error "Redis connectivity check failed"
        return 1
    fi
    
    # Check queue system
    if php artisan queue:monitor --once > /dev/null 2>&1; then
        log_success "Queue system check passed"
    else
        log_warning "Queue system check failed - may need manual intervention"
    fi
    
    return 0
}

# Start queue services
start_queue_services() {
    log "Starting queue services..."
    
    # Determine if it's peak hours and start appropriate workers
    local current_hour=$(date +"%H")
    local current_day=$(date +"%u")
    
    # Check if it's peak hours (weekday 8 AM - 6 PM)
    if [[ $current_day -le 5 ]] && [[ $current_hour -ge 8 ]] && [[ $current_hour -lt 18 ]]; then
        log "Peak hours detected - starting peak configuration"
        supervisorctl start laravel-workers-peak:*
    else
        log "Off-peak hours - starting off-peak configuration"
        supervisorctl start laravel-workers-offpeak:*
    fi
    
    # Start Horizon
    php artisan horizon &
    sleep 5
    
    # Resume queue processing
    php artisan queue:resume-all
    
    # Verify workers are running
    local active_workers=$(supervisorctl status | grep laravel-worker | grep RUNNING | wc -l)
    log_success "Queue services started - $active_workers workers active"
    
    # Process any queued jobs that accumulated during deployment
    local queued_jobs=$(redis-cli eval "
        local total = 0
        local queues = {'high', 'default', 'search', 'bulk'}
        for i, queue in ipairs(queues) do
            total = total + redis.call('llen', 'queues:' .. queue)
        end
        return total
    " 0)
    
    if [[ $queued_jobs -gt 0 ]]; then
        log "Processing $queued_jobs jobs that queued during deployment"
    fi
}

# Send deployment notification
send_deployment_notification() {
    local status=$1
    local duration=$2
    
    local message
    if [[ "$status" == "success" ]]; then
        message="DEPLOYMENT SUCCESS: SuccessionPlan AI deployed successfully in ${duration}s"
    else
        message="DEPLOYMENT FAILED: SuccessionPlan AI deployment failed after ${duration}s"
    fi
    
    php artisan queue:notification "$message"
    
    # Also log deployment metrics
    php artisan queue:metrics --deployment-event --status="$status" --duration="$duration"
}

# Main deployment function
main() {
    local start_time=$(date +%s)
    
    log "Starting SuccessionPlan AI deployment to $DEPLOY_ENV environment"
    log "Deployment started at: $(date)"
    
    # Set timeout for entire deployment
    timeout $MAX_DEPLOYMENT_TIME bash -c '
        pre_deployment_checks
        create_backup
        graceful_queue_shutdown
        deploy_application
        update_queue_configurations
        
        if health_checks; then
            start_queue_services
        else
            log_error "Health checks failed"
            exit 1
        fi
    ' || {
        log_error "Deployment timed out or failed"
        cleanup_on_error
    }
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log_success "Deployment completed successfully in ${duration} seconds"
    log "Deployment finished at: $(date)"
    
    # Send success notification
    send_deployment_notification "success" "$duration"
    
    # Show final status
    echo
    log "=== DEPLOYMENT SUMMARY ==="
    log "Environment: $DEPLOY_ENV"
    log "Duration: ${duration} seconds"
    log "Active workers: $(supervisorctl status | grep laravel-worker | grep RUNNING | wc -l)"
    log "Horizon status: $(php artisan horizon:status)"
    log "Queue sizes:"
    log "  - High: $(redis-cli llen 'queues:high' 2>/dev/null || echo 0)"
    log "  - Default: $(redis-cli llen 'queues:default' 2>/dev/null || echo 0)"
    log "  - Search: $(redis-cli llen 'queues:search' 2>/dev/null || echo 0)"
    log "=========================="
}

# Handle script arguments
case "${1:-}" in
    "check")
        pre_deployment_checks
        ;;
    "backup")
        create_backup
        ;;
    "health")
        health_checks
        ;;
    "rollback")
        log "Starting manual rollback..."
        cleanup_on_error
        ;;
    *)
        main
        ;;
esac
