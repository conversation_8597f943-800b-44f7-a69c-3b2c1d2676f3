# SuccessionPlan AI Test Suite

## Test Organization Structure

This test suite follows a clear organizational structure to make it easy to find and maintain tests.

### Directory Structure

```
tests/
├── Unit/                           # Unit tests (70% of test coverage)
│   ├── Services/                   # Service layer tests
│   │   ├── AI/                    # AI service tests (existing)
│   │   └── BusinessLogic/         # Business logic service tests
│   ├── Models/                     # Eloquent model tests
│   ├── Jobs/                      # Background job tests (existing)
│   ├── Http/                      # HTTP-related unit tests (existing)
│   ├── Helpers/                   # Helper and utility tests
│   └── Queue/                     # Queue system tests (existing)
├── Feature/                        # Integration tests (20% of test coverage)
│   ├── Api/                       # API endpoint integration tests
│   ├── Authentication/            # Auth flow integration tests
│   ├── Livewire/                  # Livewire component tests
│   ├── Jobs/                      # Job integration tests (existing)
│   └── Queue/                     # Queue integration tests (existing)
└── EndToEnd/                      # E2E tests (10% of test coverage)
    ├── UserJourneys/              # Critical user journey tests
    └── Performance/               # Performance and load tests
```

## Test Categories

### Unit Tests
- **Models**: Test Eloquent models, relationships, validation, and business logic
- **Services/AI**: Test AI service integrations with mocking
- **Services/BusinessLogic**: Test core business logic services
- **Jobs**: Test background job processing logic
- **Helpers**: Test utility functions and helper classes

### Integration Tests
- **API**: Test API endpoints with database interactions
- **Authentication**: Test complete auth flows
- **Livewire**: Test Livewire component interactions
- **Jobs**: Test job processing with queue integration

### End-to-End Tests
- **UserJourneys**: Test complete user workflows
- **Performance**: Test system performance under load

## Running Tests

```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Unit
php artisan test --testsuite=Feature

# Run tests with coverage
php artisan test --coverage

# Run specific test file
php artisan test tests/Unit/Models/SimpleModelTest.php

# Run working tests (verified to pass)
php artisan test tests/Unit/Services/AI/SimpleAiServiceTest.php tests/Unit/Models/SimpleModelTest.php tests/Unit/Helpers/SimpleErrorLoggerTest.php tests/Unit/Services/BusinessLogic/SimpleBatchServiceTest.php tests/Feature/SimpleRouteTest.php
```

## Currently Working Tests ✅

The following tests are verified to work and pass:

### Unit Tests
- **`tests/Unit/Services/AI/SimpleAiServiceTest.php`** - AI service functionality testing
- **`tests/Unit/Models/SimpleModelTest.php`** - Model structure and attribute testing
- **`tests/Unit/Helpers/SimpleErrorLoggerTest.php`** - Error logging system testing
- **`tests/Unit/Services/BusinessLogic/SimpleBatchServiceTest.php`** - Batch service structure testing

### Feature Tests
- **`tests/Feature/SimpleRouteTest.php`** - Route accessibility and authentication testing

**Total: 25 tests passing with 39 assertions**

## Test Naming Conventions

### File Naming
- Unit tests: `{ClassName}Test.php`
- Feature tests: `{FeatureName}Test.php`
- Use descriptive names that indicate what is being tested

### Method Naming
- `test_it_can_perform_action()` for positive tests
- `test_it_fails_when_invalid_data()` for negative tests
- `test_it_returns_expected_result_when_condition()` for conditional tests

## Test Data and Factories

- Use Laravel factories for generating test data
- Keep test data isolated between tests
- Use database transactions to maintain clean state

## Mocking Guidelines

- Mock external API calls (OpenAI, Anthropic, Exa)
- Mock file system operations for testing
- Mock queue operations when testing business logic
- Use Laravel's built-in mocking features

## Coverage Goals

- **Overall**: 80% code coverage
- **Critical Services**: 90% coverage
- **AI Services**: 90% coverage
- **Models**: 85% coverage
- **API Endpoints**: 85% coverage
