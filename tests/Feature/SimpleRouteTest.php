<?php

namespace Tests\Feature;

use Tests\TestCase;

class SimpleRouteTest extends TestCase
{
    /** @test */
    public function login_page_loads_successfully()
    {
        $response = $this->get('/');
        
        $response->assertStatus(200);
    }

    /** @test */
    public function forgot_password_page_loads_successfully()
    {
        $response = $this->get('/forgot-password');
        
        $response->assertStatus(200);
    }

    /** @test */
    public function clear_cache_endpoint_works()
    {
        $response = $this->get('/clear-cache');
        
        // Should redirect with success message
        $response->assertStatus(302);
    }

    /** @test */
    public function protected_routes_require_authentication()
    {
        $response = $this->get('/home');
        
        // Should redirect to login
        $response->assertRedirect();
    }

    /** @test */
    public function plan_routes_require_authentication()
    {
        $response = $this->get('/plan');
        
        // Should redirect to login
        $response->assertRedirect();
    }
}
