<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;
use App\Services\BatchService;
use App\Models\SuccessionPlan;
use App\Models\User;
use App\Jobs\SearchCandidatesJob;
use App\Jobs\CreateSuccessionPlan;

class QueueBatchProcessingTest extends TestCase
{
    use DatabaseTransactions;

    protected $user;
    protected $plan;
    protected $batchService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->plan = SuccessionPlan::factory()->create(['user_id' => $this->user->id]);
        $this->batchService = new BatchService();
    }

    public function test_succession_plan_batch_creation()
    {
        Bus::fake();

        $batch = $this->batchService->createSuccessionPlanBatch($this->plan, $this->user);

        $this->assertNotNull($batch);
        $this->assertEquals("succession-plan-{$this->plan->id}", $batch->name);
        $this->assertEquals(3, $batch->totalJobs);
        
        Bus::assertBatched(function ($batch) {
            return $batch->jobs->count() === 3 &&
                   $batch->jobs->contains(function ($job) {
                       return $job instanceof SearchCandidatesJob;
                   }) &&
                   $batch->jobs->contains(function ($job) {
                       return $job instanceof CreateSuccessionPlan;
                   });
        });
    }

    public function test_parallel_candidate_search_batch()
    {
        Bus::fake();

        $searchCriteria = [
            ['role' => 'Manager', 'location' => 'New York'],
            ['role' => 'Director', 'location' => 'London'],
            ['role' => 'VP', 'location' => 'Tokyo']
        ];

        $batch = $this->batchService->parallelCandidateSearch($this->plan, $searchCriteria);

        $this->assertNotNull($batch);
        $this->assertEquals("parallel-search-{$this->plan->id}", $batch->name);
        $this->assertEquals(count($searchCriteria), $batch->totalJobs);

        Bus::assertBatched(function ($batch) use ($searchCriteria) {
            return $batch->jobs->count() === count($searchCriteria);
        });
    }

    public function test_bulk_succession_plans_processing()
    {
        Bus::fake();

        $plans = SuccessionPlan::factory(5)->create(['user_id' => $this->user->id]);

        $batch = $this->batchService->processBulkPlans($plans, $this->user);

        $this->assertNotNull($batch);
        $this->assertEquals(5, $batch->totalJobs);
        $this->assertStringContains('bulk-succession-plans-', $batch->name);

        Bus::assertBatched(function ($batch) {
            return $batch->jobs->count() === 5 &&
                   $batch->jobs->every(function ($job) {
                       return $job instanceof CreateSuccessionPlan;
                   });
        });
    }

    public function test_batch_progress_tracking()
    {
        $batch = $this->batchService->createSuccessionPlanBatch($this->plan, $this->user);
        
        // Simulate progress callback
        cache()->put("batch_progress_{$batch->id}", [
            'processed' => 2,
            'total' => 3,
            'percentage' => 66.67,
            'failed' => 0
        ], 3600);

        $progress = $this->batchService->getBatchProgress($batch->id);

        $this->assertNotNull($progress);
        $this->assertEquals(2, $progress['processed']);
        $this->assertEquals(3, $progress['total']);
        $this->assertEquals(66.67, $progress['percentage']);
        $this->assertEquals(0, $progress['failed']);
    }

    public function test_batch_cancellation()
    {
        $batch = $this->batchService->createSuccessionPlanBatch($this->plan, $this->user);
        
        $result = $this->batchService->cancelBatch($batch->id);
        
        $this->assertTrue($result);
        
        $updatedBatch = Bus::findBatch($batch->id);
        $this->assertTrue($updatedBatch->cancelled());
    }

    public function test_batch_failure_handling()
    {
        Bus::fake();

        // Create a batch that will fail
        $batch = $this->batchService->createSuccessionPlanBatch($this->plan, $this->user);

        // Simulate batch failure by calling the catch callback
        $exception = new \Exception('Test batch failure');
        
        // This would normally be called by Laravel's batch system
        $batch->catch(function ($batch, $e) {
            $this->plan->update([
                'status' => 'failed',
                'error_message' => $e->getMessage()
            ]);
        });

        // Verify the plan was marked as failed
        $this->plan->refresh();
        $this->assertEquals('failed', $this->plan->status);
        $this->assertEquals('Test batch failure', $this->plan->error_message);
    }

    public function test_batch_completion_handling()
    {
        Bus::fake();

        $batch = $this->batchService->createSuccessionPlanBatch($this->plan, $this->user);

        // Simulate successful completion
        $batch->then(function ($batch) {
            $this->plan->update([
                'status' => 'completed',
                'completed_at' => now()
            ]);
        });

        $this->plan->refresh();
        $this->assertEquals('completed', $this->plan->status);
        $this->assertNotNull($this->plan->completed_at);
    }

    public function test_failed_batch_jobs_retry()
    {
        // This test would require actual job execution and failure simulation
        // For now, we'll test the method exists and returns expected type
        $result = $this->batchService->retryFailedBatchJobs('non-existent-batch-id');
        
        $this->assertIsBool($result);
        $this->assertFalse($result); // Should return false for non-existent batch
    }
}
