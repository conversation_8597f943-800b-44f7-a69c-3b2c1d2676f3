<?php

namespace Tests\Feature\UI;

use Tests\DatabaseTestCase;
use App\Models\User;
use App\Models\SuccessionPlan;
use App\Models\Recruitment;

class ButtonsAndActionsUITest extends DatabaseTestCase
{

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
    }

    /** @test */
    public function primary_action_buttons_exist_on_main_pages()
    {
        $pagesAndActions = [
            '/plan' => ['Add Plan', 'Create Plan', '+'],
            '/recruitment' => ['Add', 'Create', '+'],
            '/my-organization' => ['Add Organisation', 'Add Individual', 'Save'],
            '/home' => ['Home'] // At least page title should exist
        ];

        foreach ($pagesAndActions as $page => $expectedActions) {
            $response = $this->get($page);
            
            if ($response->status() === 200) {
                $content = $response->getContent();
                $hasAction = false;
                
                foreach ($expectedActions as $action) {
                    if (str_contains($content, $action)) {
                        $hasAction = true;
                        break;
                    }
                }
                
                $this->assertTrue($hasAction, "Page {$page} should have at least one primary action button");
            }
        }
    }

    /** @test */
    public function edit_buttons_exist_for_existing_items()
    {
        // Create a plan to have something to edit
        SuccessionPlan::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Test Plan'
        ]);

        $response = $this->get('/plan');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have edit functionality
        $this->assertTrue(
            str_contains($content, 'Edit') ||
            str_contains($content, 'edit') ||
            str_contains($content, 'pencil') ||
            str_contains($content, '✏') ||
            str_contains($content, 'Update')
        );
    }

    /** @test */
    public function delete_buttons_exist_for_existing_items()
    {
        // Create a plan to have something to delete
        SuccessionPlan::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Test Plan'
        ]);

        $response = $this->get('/plan');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have delete functionality
        $this->assertTrue(
            str_contains($content, 'Delete') ||
            str_contains($content, 'delete') ||
            str_contains($content, 'Remove') ||
            str_contains($content, 'trash') ||
            str_contains($content, '🗑') ||
            str_contains($content, '×')
        );
    }

    /** @test */
    public function view_details_buttons_exist()
    {
        // Create a plan to view
        SuccessionPlan::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Test Plan'
        ]);

        $response = $this->get('/plan');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have view/details functionality
        $this->assertTrue(
            str_contains($content, 'View') ||
            str_contains($content, 'Details') ||
            str_contains($content, 'Show') ||
            str_contains($content, 'Open') ||
            str_contains($content, '👁') ||
            str_contains($content, 'Report')
        );
    }

    /** @test */
    public function dropdown_menu_actions_exist()
    {
        // Create content that should have dropdown menus
        SuccessionPlan::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Test Plan'
        ]);

        $response = $this->get('/plan');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have dropdown menu indicators
        $this->assertTrue(
            str_contains($content, '⋮') ||
            str_contains($content, '...') ||
            str_contains($content, 'dropdown') ||
            str_contains($content, 'menu') ||
            str_contains($content, 'more') ||
            str_contains($content, 'actions')
        );
    }

    /** @test */
    public function save_buttons_exist_on_forms()
    {
        $response = $this->get('/my-organization');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have save functionality
        $this->assertTrue(
            str_contains($content, 'Save') ||
            str_contains($content, 'save') ||
            str_contains($content, 'Submit') ||
            str_contains($content, 'Update')
        );
    }

    /** @test */
    public function cancel_buttons_exist_on_forms()
    {
        $response = $this->get('/my-organization');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have cancel/close functionality
        $this->assertTrue(
            str_contains($content, 'Cancel') ||
            str_contains($content, 'cancel') ||
            str_contains($content, 'Close') ||
            str_contains($content, 'close') ||
            str_contains($content, '×') ||
            str_contains($content, 'Back')
        );
    }

    /** @test */
    public function upload_buttons_exist_where_needed()
    {
        $response = $this->get('/my-organization');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have upload functionality
        $this->assertTrue(
            str_contains($content, 'Upload') ||
            str_contains($content, 'upload') ||
            str_contains($content, 'Choose file') ||
            str_contains($content, 'Browse') ||
            str_contains($content, 'CSV') ||
            str_contains($content, 'file')
        );
    }

    /** @test */
    public function share_buttons_exist_where_applicable()
    {
        $response = $this->get('/my-organization');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have share functionality
        $this->assertTrue(
            str_contains($content, 'Share') ||
            str_contains($content, 'share') ||
            str_contains($content, 'Collaborate') ||
            str_contains($content, 'Export')
        );
    }

    /** @test */
    public function ai_chatbot_buttons_exist()
    {
        $response = $this->get('/plan');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have AI/chatbot functionality
        $this->assertTrue(
            str_contains($content, 'AI') ||
            str_contains($content, 'robot') ||
            str_contains($content, 'chatbot') ||
            str_contains($content, 'Chat') ||
            str_contains($content, '🤖')
        );
    }

    /** @test */
    public function export_download_buttons_exist()
    {
        // Create a plan that can be exported
        SuccessionPlan::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Test Plan'
        ]);

        $response = $this->get('/plan');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have export/download functionality
        $this->assertTrue(
            str_contains($content, 'Export') ||
            str_contains($content, 'Download') ||
            str_contains($content, 'PDF') ||
            str_contains($content, 'PowerPoint') ||
            str_contains($content, 'Report') ||
            str_contains($content, '⬇')
        );
    }

    /** @test */
    public function refresh_reload_buttons_exist()
    {
        $response = $this->get('/plan');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have refresh/reload functionality or it's automatic
        $this->assertTrue(
            str_contains($content, 'Refresh') ||
            str_contains($content, 'Reload') ||
            str_contains($content, '🔄') ||
            $response->status() === 200 // At minimum, page should load
        );
    }

    /** @test */
    public function back_navigation_buttons_exist()
    {
        // Test a detail page that should have back navigation
        $plan = SuccessionPlan::factory()->create([
            'user_id' => $this->user->id
        ]);

        $response = $this->get("/plan/{$plan->id}");

        if ($response->status() === 200) {
            $content = $response->getContent();
            
            // Should have back navigation
            $this->assertTrue(
                str_contains($content, 'Back') ||
                str_contains($content, '←') ||
                str_contains($content, 'Return') ||
                str_contains($content, 'breadcrumb')
            );
        } else {
            // If detail page doesn't exist, that's also valid
            $this->assertTrue($response->status() === 302 || $response->status() === 404);
        }
    }
}
