<?php

namespace Tests\Feature\UI;

use Tests\DatabaseTestCase;
use App\Models\User;
use App\Models\SuccessionPlan;
use Livewire\Livewire;
use App\Livewire\PlansTable;

class PlansPageUITest extends DatabaseTestCase
{

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
    }

    /** @test */
    public function plans_page_loads_with_essential_elements()
    {
        $response = $this->get('/plan');

        $response->assertStatus(200)
            ->assertSee('Plans')
            ->assertSee('Search')
            ->assertSee('Status');
    }

    /** @test */
    public function search_functionality_exists()
    {
        $response = $this->get('/plan');

        $response->assertStatus(200);
        
        // Check for search input elements in the response
        $response->assertSee('search', false); // case insensitive
    }

    /** @test */
    public function status_filter_dropdown_exists()
    {
        $response = $this->get('/plan');

        $response->assertStatus(200)
            ->assertSee('Status')
            ->assertSee('All Statuses');
    }

    /** @test */
    public function add_plan_button_exists()
    {
        $response = $this->get('/plan');

        $response->assertStatus(200);
        // Look for add/create plan button
        $content = $response->getContent();
        $this->assertTrue(
            str_contains($content, 'Add Plan') || 
            str_contains($content, 'Create Plan') ||
            str_contains($content, '+')
        );
    }

    /** @test */
    public function ai_chatbot_button_exists()
    {
        $response = $this->get('/plan');

        $response->assertStatus(200);
        // Look for AI/robot icon or chatbot button
        $content = $response->getContent();
        $this->assertTrue(
            str_contains($content, 'robot') ||
            str_contains($content, 'AI') ||
            str_contains($content, 'chatbot')
        );
    }

    /** @test */
    public function plans_display_correctly_when_present()
    {
        // Create test plans
        SuccessionPlan::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Test Senior Developer Plan',
            'status' => 'active'
        ]);

        SuccessionPlan::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Test Marketing Manager Plan', 
            'status' => 'draft'
        ]);

        $response = $this->get('/plan');

        $response->assertStatus(200)
            ->assertSee('Test Senior Developer Plan')
            ->assertSee('Test Marketing Manager Plan')
            ->assertSee('active')
            ->assertSee('draft');
    }

    /** @test */
    public function empty_state_displays_when_no_plans()
    {
        // No plans created
        $response = $this->get('/plan');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should show some empty state message
        $this->assertTrue(
            str_contains($content, 'No plans') ||
            str_contains($content, 'empty') ||
            str_contains($content, 'Create your first')
        );
    }

    /** @test */
    public function status_badges_display_correctly()
    {
        SuccessionPlan::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Active Plan',
            'status' => 'active'
        ]);

        SuccessionPlan::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Draft Plan',
            'status' => 'draft'
        ]);

        $response = $this->get('/plan');

        $response->assertStatus(200)
            ->assertSee('active')
            ->assertSee('draft');
    }

    /** @test */
    public function plan_action_buttons_exist()
    {
        SuccessionPlan::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Test Plan',
            'status' => 'active'
        ]);

        $response = $this->get('/plan');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have edit, view, or other action buttons
        $this->assertTrue(
            str_contains($content, 'Edit') ||
            str_contains($content, 'View') ||
            str_contains($content, 'People Search') ||
            str_contains($content, 'Report')
        );
    }
}
