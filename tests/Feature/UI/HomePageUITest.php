<?php

namespace Tests\Feature\UI;

use Tests\DatabaseTestCase;
use App\Models\User;
use App\Models\notifications;

class HomePageUITest extends DatabaseTestCase
{

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
    }

    /** @test */
    public function home_page_loads_with_required_elements()
    {
        $response = $this->get('/home');

        $response->assertStatus(200)
            ->assertSee('Home')
            ->assertSee('Notifications')
            ->assertSee('Calendar')
            ->assertViewIs('home');
    }

    /** @test */
    public function notifications_panel_displays_correctly()
    {
        // Create test notifications
        notifications::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'Plan Created',
            'message' => 'Test notification message'
        ]);

        $response = $this->get('/home');

        $response->assertStatus(200)
            ->assertSee('Test notification message')
            ->assertSee('Plan Created');
    }

    /** @test */
    public function delete_notification_button_exists()
    {
        notifications::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'Plan Created',
            'message' => 'Test notification'
        ]);

        $response = $this->get('/home');

        // Should have delete button/icon
        $response->assertStatus(200)
            ->assertSeeText('Test notification');
    }

    /** @test */
    public function calendar_section_exists()
    {
        $response = $this->get('/home');

        $response->assertStatus(200)
            ->assertSee('Calendar');
    }

    /** @test */
    public function navigation_sidebar_is_present()
    {
        $response = $this->get('/home');

        $response->assertStatus(200)
            ->assertSee('Home')
            ->assertSee('Plans')
            ->assertSee('My Organization');
    }

    /** @test */
    public function user_can_access_main_navigation_links()
    {
        // Test Plans link
        $response = $this->get('/plan');
        $response->assertStatus(200);

        // Test My Organization link  
        $response = $this->get('/my-organization');
        $response->assertStatus(200);

        // Test Recruitment link
        $response = $this->get('/recruitment');
        $response->assertStatus(200);
    }
}
