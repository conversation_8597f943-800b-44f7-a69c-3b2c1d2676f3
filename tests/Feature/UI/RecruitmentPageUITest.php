<?php

namespace Tests\Feature\UI;

use Tests\DatabaseTestCase;
use App\Models\User;
use App\Models\Recruitment;

class RecruitmentPageUITest extends DatabaseTestCase
{

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
    }

    /** @test */
    public function recruitment_page_loads_with_essential_elements()
    {
        $response = $this->get('/recruitment');

        $response->assertStatus(200)
            ->assertSee('Recruitment');
    }

    /** @test */
    public function add_recruitment_button_exists()
    {
        $response = $this->get('/recruitment');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have add/create button
        $this->assertTrue(
            str_contains($content, 'Add') ||
            str_contains($content, 'Create') ||
            str_contains($content, '+')
        );
    }

    /** @test */
    public function search_functionality_exists()
    {
        $response = $this->get('/recruitment');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have search input
        $this->assertTrue(
            str_contains($content, 'search') ||
            str_contains($content, 'Search')
        );
    }

    /** @test */
    public function status_filter_exists()
    {
        $response = $this->get('/recruitment');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have status filter
        $this->assertTrue(
            str_contains($content, 'Status') ||
            str_contains($content, 'Filter')
        );
    }

    /** @test */
    public function recruitment_cards_display_when_present()
    {
        // Create test recruitment
        Recruitment::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Test Senior Developer Recruitment',
            'status' => 'active'
        ]);

        $response = $this->get('/recruitment');

        $response->assertStatus(200)
            ->assertSee('Test Senior Developer Recruitment');
    }

    /** @test */
    public function recruitment_action_menu_exists()
    {
        Recruitment::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Test Recruitment'
        ]);

        $response = $this->get('/recruitment');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have three-dot menu or action buttons
        $this->assertTrue(
            str_contains($content, '⋮') ||
            str_contains($content, '...') ||
            str_contains($content, 'View') ||
            str_contains($content, 'Edit') ||
            str_contains($content, 'Delete')
        );
    }

    /** @test */
    public function progress_indicators_display()
    {
        Recruitment::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Test Recruitment'
        ]);

        $response = $this->get('/recruitment');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should show progress or status indicators
        $this->assertTrue(
            str_contains($content, 'progress') ||
            str_contains($content, '%') ||
            str_contains($content, 'ratio') ||
            str_contains($content, 'gender')
        );
    }

    /** @test */
    public function empty_state_displays_when_no_recruitments()
    {
        $response = $this->get('/recruitment');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should show empty state message
        $this->assertTrue(
            str_contains($content, 'No recruitment') ||
            str_contains($content, 'empty') ||
            str_contains($content, 'Create your first')
        );
    }
}
