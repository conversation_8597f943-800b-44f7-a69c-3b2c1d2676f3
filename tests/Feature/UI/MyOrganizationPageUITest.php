<?php

namespace Tests\Feature\UI;

use Tests\DatabaseTestCase;
use App\Models\User;
use App\Models\Organisation;

class MyOrganizationPageUITest extends DatabaseTestCase
{

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
    }

    /** @test */
    public function my_organization_page_loads_with_essential_elements()
    {
        $response = $this->get('/my-organization');

        $response->assertStatus(200)
            ->assertSee('My Organization');
    }

    /** @test */
    public function header_action_buttons_exist()
    {
        $response = $this->get('/my-organization');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have key action buttons
        $actionButtons = [
            'Search Internal People',
            'Share',
            'Nine Box Grid', 
            'Add Organisation',
            'Add Individual',
            'Competencies',
            'Upload',
            'Save'
        ];
        
        $hasActionButtons = false;
        foreach ($actionButtons as $button) {
            if (str_contains($content, $button)) {
                $hasActionButtons = true;
                break;
            }
        }
        
        $this->assertTrue($hasActionButtons, 'At least one action button should be present');
    }

    /** @test */
    public function upload_functionality_button_exists()
    {
        $response = $this->get('/my-organization');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have upload button for CSV
        $this->assertTrue(
            str_contains($content, 'Upload') ||
            str_contains($content, 'upload') ||
            str_contains($content, 'CSV')
        );
    }

    /** @test */
    public function search_internal_people_button_exists()
    {
        $response = $this->get('/my-organization');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have search internal people functionality
        $this->assertTrue(
            str_contains($content, 'Search Internal') ||
            str_contains($content, 'Internal People') ||
            str_contains($content, 'Search People')
        );
    }

    /** @test */
    public function nine_box_grid_button_exists()
    {
        $response = $this->get('/my-organization');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have nine box grid functionality
        $this->assertTrue(
            str_contains($content, 'Nine Box') ||
            str_contains($content, 'Grid') ||
            str_contains($content, '9 Box')
        );
    }

    /** @test */
    public function add_organization_button_exists()
    {
        $response = $this->get('/my-organization');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have add organization functionality
        $this->assertTrue(
            str_contains($content, 'Add Organisation') ||
            str_contains($content, 'Add Organization') ||
            str_contains($content, 'Create Organisation')
        );
    }

    /** @test */
    public function add_individual_button_exists()
    {
        $response = $this->get('/my-organization');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have add individual functionality
        $this->assertTrue(
            str_contains($content, 'Add Individual') ||
            str_contains($content, 'Add Person') ||
            str_contains($content, 'New Individual')
        );
    }

    /** @test */
    public function competencies_button_exists()
    {
        $response = $this->get('/my-organization');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have competencies functionality
        $this->assertTrue(
            str_contains($content, 'Competencies') ||
            str_contains($content, 'Competency') ||
            str_contains($content, 'Skills')
        );
    }

    /** @test */
    public function share_button_exists()
    {
        $response = $this->get('/my-organization');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have share functionality
        $this->assertTrue(
            str_contains($content, 'Share') ||
            str_contains($content, 'share')
        );
    }

    /** @test */
    public function save_organization_button_exists()
    {
        $response = $this->get('/my-organization');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have save functionality
        $this->assertTrue(
            str_contains($content, 'Save') ||
            str_contains($content, 'save')
        );
    }

    /** @test */
    public function organization_chart_area_exists()
    {
        $response = $this->get('/my-organization');

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should have organization chart area
        $this->assertTrue(
            str_contains($content, 'chart') ||
            str_contains($content, 'organization') ||
            str_contains($content, 'hierarchy') ||
            str_contains($content, 'org-chart')
        );
    }

    /** @test */
    public function displays_organization_data_when_present()
    {
        // Create test organization
        Organisation::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Test Department',
            'role' => 'Manager'
        ]);

        $response = $this->get('/my-organization');

        $response->assertStatus(200)
            ->assertSee('Test Department');
    }
}
