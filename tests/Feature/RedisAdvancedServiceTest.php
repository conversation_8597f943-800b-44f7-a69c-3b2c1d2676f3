<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Redis;
use Tests\TestCase;
use App\Services\RedisAdvancedService;

class RedisAdvancedServiceTest extends TestCase
{
    use DatabaseTransactions;

    protected $redisService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->redisService = new RedisAdvancedService();
        
        // Clean Redis before each test
        Redis::flushall();
    }

    public function test_batch_job_metrics_update()
    {
        $metrics = [
            'job_1' => ['status' => 'completed', 'duration' => 120, 'memory' => 64],
            'job_2' => ['status' => 'failed', 'duration' => 60, 'error' => 'Connection timeout'],
            'job_3' => ['status' => 'running', 'duration' => null, 'started_at' => now()]
        ];

        $result = $this->redisService->batchJobMetrics($metrics);

        $this->assertTrue($result);

        // Verify metrics were stored
        $this->assertTrue(Redis::hexists('job_metrics', 'job_1'));
        $this->assertTrue(Redis::hexists('job_metrics', 'job_2'));
        $this->assertTrue(Redis::hexists('job_metrics', 'job_3'));

        // Verify counters were incremented
        $this->assertEquals(1, Redis::get('job_count:completed'));
        $this->assertEquals(1, Redis::get('job_count:failed'));
        $this->assertEquals(1, Redis::get('job_count:running'));

        // Verify timeline entries
        $timelineCount = Redis::zcard('job_metrics_timeline');
        $this->assertEquals(3, $timelineCount);
    }

    public function test_atomic_queue_operation()
    {
        // Setup source queue with test jobs
        Redis::lpush('queues:source', 'job1', 'job2', 'job3', 'job4', 'job5');
        
        $movedJobs = $this->redisService->atomicQueueOperation('source', 'target', 3);

        $this->assertEquals(3, $movedJobs);
        
        // Verify jobs were moved
        $this->assertEquals(2, Redis::llen('queues:source')); // 5 - 3 = 2 remaining
        $this->assertEquals(3, Redis::llen('queues:target'));  // 3 moved
        
        // Verify counters were updated
        $this->assertEquals(3, Redis::get('queue_stats:source:moved_out'));
        $this->assertEquals(3, Redis::get('queue_stats:target:moved_in'));
        $this->assertTrue(Redis::exists('queue_stats:last_move'));
    }

    public function test_queue_event_logging()
    {
        $eventData = [
            'job_class' => 'SearchCandidatesJob',
            'queue' => 'high',
            'status' => 'started'
        ];

        $eventId = $this->redisService->logQueueEvent('job_started', $eventData);

        $this->assertNotEmpty($eventId);
        
        // Verify event was added to stream
        $events = Redis::xrange('queue_events', '-', '+');
        $this->assertCount(1, $events);
        
        $event = array_values($events)[0];
        $this->assertEquals('job_started', $event['event']);
        $this->assertEquals('SearchCandidatesJob', $event['job_class']);
    }

    public function test_read_queue_events()
    {
        // Add some test events
        $this->redisService->logQueueEvent('job_started', ['job_id' => 1]);
        $this->redisService->logQueueEvent('job_completed', ['job_id' => 1]);
        $this->redisService->logQueueEvent('job_failed', ['job_id' => 2]);

        $events = $this->redisService->readQueueEvents(10);

        $this->assertCount(3, $events);
        $this->assertEquals('job_started', $events[0]['data']['event']);
        $this->assertEquals('job_completed', $events[1]['data']['event']);
        $this->assertEquals('job_failed', $events[2]['data']['event']);
    }

    public function test_distributed_lock_acquisition()
    {
        $lockValue = $this->redisService->acquireDistributedLock('test_resource', 30);

        $this->assertNotNull($lockValue);
        $this->assertTrue(Redis::exists('lock:test_resource'));
        
        // Verify TTL was set
        $ttl = Redis::ttl('lock:test_resource');
        $this->assertGreaterThan(0, $ttl);
        $this->assertLessThanOrEqual(30, $ttl);
    }

    public function test_distributed_lock_prevents_duplicate_acquisition()
    {
        $lockValue1 = $this->redisService->acquireDistributedLock('test_resource', 30);
        $lockValue2 = $this->redisService->acquireDistributedLock('test_resource', 30);

        $this->assertNotNull($lockValue1);
        $this->assertNull($lockValue2);
    }

    public function test_distributed_lock_release()
    {
        $lockValue = $this->redisService->acquireDistributedLock('test_resource', 30);
        $this->assertNotNull($lockValue);

        $released = $this->redisService->releaseDistributedLock('test_resource', $lockValue);
        $this->assertTrue($released);
        $this->assertFalse(Redis::exists('lock:test_resource'));
    }

    public function test_distributed_lock_release_with_wrong_value()
    {
        $lockValue = $this->redisService->acquireDistributedLock('test_resource', 30);
        $this->assertNotNull($lockValue);

        $released = $this->redisService->releaseDistributedLock('test_resource', 'wrong_value');
        $this->assertFalse($released);
        $this->assertTrue(Redis::exists('lock:test_resource')); // Lock should still exist
    }

    public function test_unique_jobs_tracking()
    {
        $result1 = $this->redisService->trackUniqueJobsProcessed('SearchCandidatesJob');
        $result2 = $this->redisService->trackUniqueJobsProcessed('CreateSuccessionPlan');
        $result3 = $this->redisService->trackUniqueJobsProcessed('SearchCandidatesJob');

        $this->assertTrue($result1);
        $this->assertTrue($result2);
        $this->assertTrue($result3);

        $uniqueCount = $this->redisService->getUniqueJobsProcessedToday();
        $this->assertGreaterThan(0, $uniqueCount);
        $this->assertLessThanOrEqual(3, $uniqueCount); // Due to HyperLogLog approximation
    }

    public function test_sliding_window_rate_limiting()
    {
        $key = 'test_rate_limit';
        $maxRequests = 3;
        $windowSize = 60;

        // First 3 requests should be allowed
        $this->assertFalse($this->redisService->isRateLimited($key, $maxRequests, $windowSize));
        $this->assertFalse($this->redisService->isRateLimited($key, $maxRequests, $windowSize));
        $this->assertFalse($this->redisService->isRateLimited($key, $maxRequests, $windowSize));

        // 4th request should be rate limited
        $this->assertTrue($this->redisService->isRateLimited($key, $maxRequests, $windowSize));
    }

    public function test_bulk_update_job_statuses()
    {
        $jobUpdates = [
            ['job_id' => 'job_1', 'status' => 'completed', 'timestamp' => now()->toDateTimeString()],
            ['job_id' => 'job_2', 'status' => 'failed', 'timestamp' => now()->toDateTimeString()],
            ['job_id' => 'job_3', 'status' => 'running', 'timestamp' => now()->toDateTimeString()]
        ];

        $result = $this->redisService->bulkUpdateJobStatuses($jobUpdates);

        $this->assertTrue($result);

        // Verify job statuses were updated
        $this->assertEquals('completed', Redis::hget('job_status:job_1', 'status'));
        $this->assertEquals('failed', Redis::hget('job_status:job_2', 'status'));
        $this->assertEquals('running', Redis::hget('job_status:job_3', 'status'));

        // Verify status indices were updated
        $this->assertTrue(Redis::sismember('jobs_by_status:completed', 'job_1'));
        $this->assertTrue(Redis::sismember('jobs_by_status:failed', 'job_2'));
        $this->assertTrue(Redis::sismember('jobs_by_status:running', 'job_3'));

        // Verify counters were incremented
        $this->assertEquals(1, Redis::get('job_count:completed'));
        $this->assertEquals(1, Redis::get('job_count:failed'));
        $this->assertEquals(1, Redis::get('job_count:running'));
    }

    public function test_cleanup_expired_keys()
    {
        // Create some test keys without TTL
        Redis::set('job_temp_123', 'data');
        Redis::set('job_result_456', 'result');
        Redis::set('job_error_789', 'error');
        Redis::set('other_key', 'should_not_be_affected');

        $cleaned = $this->redisService->cleanupExpiredKeys(1000);

        $this->assertEquals(3, $cleaned);

        // Verify TTL was set on job keys
        $this->assertGreaterThan(0, Redis::ttl('job_temp_123'));
        $this->assertGreaterThan(0, Redis::ttl('job_result_456'));
        $this->assertGreaterThan(0, Redis::ttl('job_error_789'));

        // Verify other keys were not affected
        $this->assertEquals(-1, Redis::ttl('other_key')); // -1 means no TTL set
    }

    public function test_pipeline_performance()
    {
        $startTime = microtime(true);

        // Test batch operations performance
        $largeMetricsSet = [];
        for ($i = 0; $i < 100; $i++) {
            $largeMetricsSet["job_$i"] = [
                'status' => ($i % 2 === 0) ? 'completed' : 'failed',
                'duration' => rand(10, 300),
                'memory' => rand(32, 128)
            ];
        }

        $result = $this->redisService->batchJobMetrics($largeMetricsSet);
        
        $executionTime = microtime(true) - $startTime;

        $this->assertTrue($result);
        $this->assertLessThan(1.0, $executionTime); // Should complete in less than 1 second

        // Verify all metrics were stored
        $this->assertEquals(100, Redis::hlen('job_metrics'));
    }

    protected function tearDown(): void
    {
        Redis::flushall();
        parent::tearDown();
    }
}
