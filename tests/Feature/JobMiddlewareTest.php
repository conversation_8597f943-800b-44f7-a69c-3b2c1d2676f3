<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Redis;
use Tests\TestCase;
use App\Jobs\SearchCandidatesJob;
use App\Http\Middleware\JobMiddleware\RateLimitJobs;
use App\Http\Middleware\JobMiddleware\SkipDuplicateJobs;
use App\Http\Middleware\JobMiddleware\ConditionalJobExecution;
use App\Models\User;
use App\Models\SuccessionPlan;

class JobMiddlewareTest extends TestCase
{
    use DatabaseTransactions;

    protected $user;
    protected $planData;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $plan = SuccessionPlan::factory()->create(['user_id' => $this->user->id]);
        
        $this->planData = [
            'plan_id' => $plan->id,
            'plan_name' => $plan->name,
            'target_roles' => ['Manager', 'Director'],
            'companies' => ['Company A', 'Company B']
        ];
    }

    public function test_rate_limit_middleware_allows_within_limits()
    {
        Redis::flushall();
        
        $middleware = new RateLimitJobs();
        $job = new SearchCandidatesJob($this->planData, $this->user);
        
        $executed = false;
        $next = function () use (&$executed) {
            $executed = true;
        };
        
        $middleware->handle($job, $next);
        
        $this->assertTrue($executed);
    }

    public function test_rate_limit_middleware_blocks_when_exceeded()
    {
        Redis::flushall();
        
        $middleware = new RateLimitJobs();
        $job = new SearchCandidatesJob($this->planData, $this->user);
        
        // Create a job that will exceed rate limits
        $rateLimitJob = new class($this->planData, $this->user) extends SearchCandidatesJob {
            public function getMaxAttempts(): int
            {
                return 1; // Very low limit for testing
            }
            
            public function getRateLimitKey(): string
            {
                return 'test_rate_limit_key';
            }
        };
        
        // Set the rate limit counter to exceed the limit
        Redis::set('test_rate_limit_key', 5); // Above the limit
        
        $executed = false;
        $next = function () use (&$executed) {
            $executed = true;
        };
        
        $middleware->handle($rateLimitJob, $next);
        
        $this->assertFalse($executed);
    }

    public function test_duplicate_job_middleware_skips_duplicates()
    {
        Redis::flushall();
        
        $middleware = new SkipDuplicateJobs();
        $job1 = new SearchCandidatesJob($this->planData, $this->user);
        $job2 = new SearchCandidatesJob($this->planData, $this->user);
        
        $executed1 = false;
        $executed2 = false;
        
        $next1 = function () use (&$executed1) {
            $executed1 = true;
        };
        
        $next2 = function () use (&$executed2) {
            $executed2 = true;
        };
        
        // First job should execute
        $middleware->handle($job1, $next1);
        $this->assertTrue($executed1);
        
        // Second identical job should be skipped
        $result = $middleware->handle($job2, $next2);
        $this->assertFalse($executed2);
    }

    public function test_conditional_execution_middleware_outside_business_hours()
    {
        $middleware = new ConditionalJobExecution();
        
        // Create a non-critical job
        $job = new SearchCandidatesJob($this->planData, $this->user);
        
        $executed = false;
        $next = function () use (&$executed) {
            $executed = true;
        };
        
        // Mock time to be outside business hours (e.g., 2 AM on Sunday)
        $this->travelTo(now()->startOfWeek()->subDay()->setTime(2, 0));
        
        $middleware->handle($job, $next);
        
        // Job should be delayed, not executed immediately
        $this->assertFalse($executed);
    }

    public function test_conditional_execution_middleware_during_maintenance()
    {
        $middleware = new ConditionalJobExecution();
        $job = new SearchCandidatesJob($this->planData, $this->user);
        
        // Set maintenance mode
        cache()->put('system_maintenance', true, 3600);
        
        $executed = false;
        $next = function () use (&$executed) {
            $executed = true;
        };
        
        $middleware->handle($job, $next);
        
        // Job should be delayed during maintenance
        $this->assertFalse($executed);
        
        // Clean up
        cache()->forget('system_maintenance');
    }

    public function test_conditional_execution_middleware_allows_critical_jobs()
    {
        $middleware = new ConditionalJobExecution();
        
        // CreateSuccessionPlan is marked as critical in the middleware
        $criticalJobData = array_merge($this->planData, ['is_critical' => true]);
        $job = new \App\Jobs\CreateSuccessionPlan(
            SuccessionPlan::factory()->create($criticalJobData), 
            $this->user
        );
        
        $executed = false;
        $next = function () use (&$executed) {
            $executed = true;
        };
        
        // Mock time to be outside business hours
        $this->travelTo(now()->startOfWeek()->subDay()->setTime(2, 0));
        
        $middleware->handle($job, $next);
        
        // Critical jobs should execute even outside business hours
        $this->assertTrue($executed);
    }

    public function test_job_middleware_integration()
    {
        Queue::fake();
        
        $job = new SearchCandidatesJob($this->planData, $this->user);
        
        // Verify middleware is properly configured
        $middleware = $job->middleware();
        
        $this->assertCount(3, $middleware);
        $this->assertInstanceOf(RateLimitJobs::class, $middleware[0]);
        $this->assertInstanceOf(SkipDuplicateJobs::class, $middleware[1]);
        $this->assertInstanceOf(ConditionalJobExecution::class, $middleware[2]);
    }

    public function test_job_duplicate_key_generation()
    {
        $job = new SearchCandidatesJob($this->planData, $this->user);
        
        $duplicateKey = $job->getDuplicateKey();
        
        $this->assertStringContains('search_job:', $duplicateKey);
        $this->assertStringContains((string)$this->planData['plan_id'], $duplicateKey);
        $this->assertStringContains('both', $duplicateKey); // default search type
    }

    public function test_job_rate_limit_key_generation()
    {
        $job = new SearchCandidatesJob($this->planData, $this->user);
        
        $rateLimitKey = $job->getRateLimitKey();
        
        $this->assertStringContains('search_rate_limit:', $rateLimitKey);
        $this->assertStringContains((string)$this->planData['plan_id'], $rateLimitKey);
    }

    public function test_middleware_chain_execution_order()
    {
        $executionOrder = [];
        
        $job = new class($this->planData, $this->user) extends SearchCandidatesJob {
            private $executionOrder;
            
            public function __construct($planData, $user)
            {
                parent::__construct($planData, $user);
                $this->executionOrder = &$GLOBALS['test_execution_order'];
            }
            
            public function middleware(): array
            {
                return [
                    new class {
                        public function handle($job, $next) {
                            $GLOBALS['test_execution_order'][] = 'first';
                            return $next($job);
                        }
                    },
                    new class {
                        public function handle($job, $next) {
                            $GLOBALS['test_execution_order'][] = 'second';
                            return $next($job);
                        }
                    }
                ];
            }
        };
        
        $GLOBALS['test_execution_order'] = [];
        
        Queue::fake();
        
        // Manually execute middleware chain
        $next = function () {
            $GLOBALS['test_execution_order'][] = 'job_executed';
        };
        
        $middleware = $job->middleware();
        $middleware[0]->handle($job, function ($job) use ($middleware, $next) {
            $middleware[1]->handle($job, $next);
        });
        
        $this->assertEquals(['first', 'second', 'job_executed'], $GLOBALS['test_execution_order']);
    }
}
