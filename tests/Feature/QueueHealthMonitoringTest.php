<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Redis;
use Tests\TestCase;

class QueueHealthMonitoringTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Clean Redis before each test
        Redis::flushall();
    }

    public function test_queue_health_metrics_storage()
    {
        // Simulate storing health metrics
        $timestamp = now()->toDateTimeString();
        $healthData = [
            'timestamp' => $timestamp,
            'high_queue' => 5,
            'default_queue' => 10,
            'search_queue' => 3,
            'bulk_queue' => 0,
            'total_queued' => 18,
            'failed_jobs' => 2,
            'worker_health' => '8/10',
            'redis_health' => 'healthy',
            'horizon_health' => 'running',
            'processing_rates' => '1min:15 5min:72 15min:210',
            'stuck_jobs' => 0
        ];

        Redis::hmset('queue_health:latest', $healthData);
        Redis::expire('queue_health:latest', 3600);

        // Add to time series
        $timeSeriesData = implode('|', [
            $timestamp,
            $healthData['total_queued'],
            $healthData['failed_jobs'],
            $healthData['worker_health']
        ]);
        
        Redis::zadd('queue_health:history', time(), $timeSeriesData);

        // Verify data was stored correctly
        $storedData = Redis::hgetall('queue_health:latest');
        
        $this->assertEquals($healthData['total_queued'], $storedData['total_queued']);
        $this->assertEquals($healthData['failed_jobs'], $storedData['failed_jobs']);
        $this->assertEquals($healthData['redis_health'], $storedData['redis_health']);
        $this->assertEquals($healthData['horizon_health'], $storedData['horizon_health']);

        // Verify time series data
        $historyCount = Redis::zcard('queue_health:history');
        $this->assertEquals(1, $historyCount);
    }

    public function test_queue_alert_logging()
    {
        $alerts = [
            ['level' => 'WARNING', 'message' => 'Queue size is high: 150 jobs'],
            ['level' => 'CRITICAL', 'message' => 'Redis is unhealthy'],
            ['level' => 'INFO', 'message' => 'Workers restarted successfully']
        ];

        foreach ($alerts as $alert) {
            $alertData = date('Y-m-d H:i:s') . '|' . $alert['level'] . '|' . $alert['message'];
            Redis::lpush('queue_alerts', $alertData);
        }

        // Trim to keep only last 999 alerts (simulating the script behavior)
        Redis::ltrim('queue_alerts', 0, 999);

        $storedAlerts = Redis::lrange('queue_alerts', 0, -1);
        
        $this->assertCount(3, $storedAlerts);
        $this->assertStringContains('CRITICAL', $storedAlerts[1]);
        $this->assertStringContains('Redis is unhealthy', $storedAlerts[1]);
    }

    public function test_queue_size_monitoring()
    {
        // Simulate different queue sizes
        Redis::lpush('queues:high', 'job1', 'job2', 'job3');
        Redis::lpush('queues:default', 'job4', 'job5');
        Redis::lpush('queues:search', 'job6');
        Redis::lpush('queues:bulk', 'job7', 'job8', 'job9', 'job10');

        // Test queue size retrieval
        $highQueueSize = Redis::llen('queues:high');
        $defaultQueueSize = Redis::llen('queues:default');
        $searchQueueSize = Redis::llen('queues:search');
        $bulkQueueSize = Redis::llen('queues:bulk');

        $this->assertEquals(3, $highQueueSize);
        $this->assertEquals(2, $defaultQueueSize);
        $this->assertEquals(1, $searchQueueSize);
        $this->assertEquals(4, $bulkQueueSize);

        $totalQueued = $highQueueSize + $defaultQueueSize + $searchQueueSize + $bulkQueueSize;
        $this->assertEquals(10, $totalQueued);
    }

    public function test_failed_jobs_monitoring()
    {
        // Simulate failed jobs
        $failedJobs = [
            'failed_job_1' => json_encode(['error' => 'Connection timeout', 'job' => 'SearchCandidatesJob']),
            'failed_job_2' => json_encode(['error' => 'Invalid data', 'job' => 'CreateSuccessionPlan']),
            'failed_job_3' => json_encode(['error' => 'API limit exceeded', 'job' => 'ExternalSearchJob'])
        ];

        foreach ($failedJobs as $jobId => $jobData) {
            Redis::lpush('queues:failed', $jobData);
        }

        $failedJobsCount = Redis::llen('queues:failed');
        $this->assertEquals(3, $failedJobsCount);

        // Test failed job data retrieval
        $latestFailedJob = Redis::lindex('queues:failed', 0);
        $jobData = json_decode($latestFailedJob, true);
        
        $this->assertArrayHasKey('error', $jobData);
        $this->assertArrayHasKey('job', $jobData);
    }

    public function test_worker_status_simulation()
    {
        // Simulate supervisor status output
        $supervisorStatus = [
            'laravel-worker-offpeak:laravel-worker-offpeak_00' => 'RUNNING',
            'laravel-worker-offpeak:laravel-worker-offpeak_01' => 'RUNNING',
            'laravel-worker-search-offpeak:laravel-worker-search-offpeak_00' => 'RUNNING',
            'laravel-worker-search-offpeak:laravel-worker-search-offpeak_01' => 'STOPPED',
            'laravel-worker-maintenance:laravel-worker-maintenance_00' => 'RUNNING'
        ];

        $runningWorkers = 0;
        $totalWorkers = count($supervisorStatus);

        foreach ($supervisorStatus as $worker => $status) {
            if ($status === 'RUNNING') {
                $runningWorkers++;
            }
        }

        $workerHealth = "$runningWorkers/$totalWorkers";
        
        $this->assertEquals('4/5', $workerHealth);
        $this->assertEquals(80, ($runningWorkers / $totalWorkers) * 100); // 80% healthy
    }

    public function test_redis_health_check()
    {
        // Test Redis connectivity
        try {
            $pingResult = Redis::ping();
            $redisHealth = 'healthy';
        } catch (\Exception $e) {
            $redisHealth = 'unhealthy';
        }

        $this->assertEquals('healthy', $redisHealth);

        // Test Redis operations
        Redis::set('health_check_key', 'test_value');
        $retrievedValue = Redis::get('health_check_key');
        
        $this->assertEquals('test_value', $retrievedValue);
        
        Redis::del('health_check_key');
    }

    public function test_stuck_jobs_detection()
    {
        $currentTime = time();
        $maxWaitTime = 3600; // 1 hour
        
        // Simulate processing jobs with different start times
        $processingJobs = [
            'job_1' => $currentTime - 1800, // 30 minutes ago (not stuck)
            'job_2' => $currentTime - 4200, // 70 minutes ago (stuck)
            'job_3' => $currentTime - 7200, // 2 hours ago (stuck)
            'job_4' => $currentTime - 300,  // 5 minutes ago (not stuck)
        ];

        // Add jobs to processing set and store start times
        foreach ($processingJobs as $jobId => $startTime) {
            Redis::sadd('jobs:processing', $jobId);
            Redis::hset("job:$jobId", 'started_at', $startTime);
        }

        // Count stuck jobs
        $stuckJobs = 0;
        $processingJobsList = Redis::smembers('jobs:processing');
        
        foreach ($processingJobsList as $jobId) {
            $startTime = Redis::hget("job:$jobId", 'started_at');
            if ($startTime && ($currentTime - $startTime) > $maxWaitTime) {
                $stuckJobs++;
            }
        }

        $this->assertEquals(2, $stuckJobs); // job_2 and job_3 should be stuck
    }

    public function test_processing_rate_calculation()
    {
        // Simulate job completion counts over different time periods
        Redis::set('queue_stats:processed_1min', 15);
        Redis::set('queue_stats:processed_5min', 72);
        Redis::set('queue_stats:processed_15min', 210);

        $rates = [
            '1min' => Redis::get('queue_stats:processed_1min'),
            '5min' => Redis::get('queue_stats:processed_5min'),
            '15min' => Redis::get('queue_stats:processed_15min')
        ];

        $this->assertEquals(15, $rates['1min']);
        $this->assertEquals(72, $rates['5min']);
        $this->assertEquals(210, $rates['15min']);

        // Calculate jobs per minute rates
        $ratePerMinute1 = $rates['1min'] / 1;    // 15 jobs/min
        $ratePerMinute5 = $rates['5min'] / 5;    // 14.4 jobs/min
        $ratePerMinute15 = $rates['15min'] / 15; // 14 jobs/min

        $this->assertEquals(15, $ratePerMinute1);
        $this->assertEquals(14.4, $ratePerMinute5);
        $this->assertEquals(14, $ratePerMinute15);
    }

    public function test_threshold_breach_detection()
    {
        $alertThresholds = [
            'high' => 100,
            'critical' => 500
        ];

        $testScenarios = [
            ['queue_size' => 50, 'expected_level' => null],
            ['queue_size' => 150, 'expected_level' => 'WARNING'],
            ['queue_size' => 600, 'expected_level' => 'CRITICAL']
        ];

        foreach ($testScenarios as $scenario) {
            $queueSize = $scenario['queue_size'];
            $expectedLevel = $scenario['expected_level'];
            
            $alertLevel = null;
            
            if ($queueSize > $alertThresholds['critical']) {
                $alertLevel = 'CRITICAL';
            } elseif ($queueSize > $alertThresholds['high']) {
                $alertLevel = 'WARNING';
            }
            
            $this->assertEquals($expectedLevel, $alertLevel);
        }
    }

    public function test_health_history_retention()
    {
        // Add multiple health records with timestamps
        for ($i = 0; $i < 1500; $i++) { // More than the 1440 limit (24 hours)
            $timestamp = time() - ($i * 60); // Each record 1 minute apart
            $data = "2025-01-01 12:00:00|10|2|8/10";
            
            Redis::zadd('queue_health:history', $timestamp, $data);
        }

        // Simulate the cleanup that keeps only last 1440 records (24 hours)
        Redis::zremrangebyrank('queue_health:history', 0, -1441);

        $remainingRecords = Redis::zcard('queue_health:history');
        
        $this->assertLessThanOrEqual(1440, $remainingRecords);
    }

    protected function tearDown(): void
    {
        Redis::flushall();
        parent::tearDown();
    }
}
