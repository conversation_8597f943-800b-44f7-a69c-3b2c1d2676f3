<?php

namespace Tests\Unit\Services\AI;

use Tests\TestCase;
use App\Services\AI\RateLimitedOpenAiService;

class SimpleAiServiceTest extends TestCase
{
    /** @test */
    public function it_can_be_instantiated()
    {
        $service = new RateLimitedOpenAiService();
        $this->assertInstanceOf(RateLimitedOpenAiService::class, $service);
    }

    /** @test */
    public function it_provides_rate_limit_status()
    {
        $service = new RateLimitedOpenAiService();
        
        $status = $service->getRateLimitStatus('test-key');
        
        $this->assertIsArray($status);
        $this->assertArrayHasKey('rpm', $status);
        $this->assertArrayHasKey('tpm', $status);
    }

    /** @test */
    public function it_can_clear_rate_limits()
    {
        $service = new RateLimitedOpenAiService();
        
        // This should not throw an exception
        $service->clearRateLimit('test-key');
        
        $this->assertTrue(true);
    }

    /** @test */
    public function it_can_check_circuit_breaker_status()
    {
        $service = new RateLimitedOpenAiService();
        
        $isOpen = $service->isCircuitOpen('test-key');
        
        $this->assertIsBool($isOpen);
        $this->assertFalse($isOpen); // Should be closed initially
    }
}
