<?php

namespace Tests\Unit\Services\BusinessLogic;

use Tests\TestCase;
use App\Services\BatchService;

class SimpleBatchServiceTest extends TestCase
{
    /** @test */
    public function it_can_be_instantiated()
    {
        $service = new BatchService();
        $this->assertInstanceOf(BatchService::class, $service);
    }

    /** @test */
    public function batch_service_class_exists()
    {
        $this->assertTrue(class_exists(BatchService::class));
    }

    /** @test */
    public function it_has_create_succession_plan_batch_method()
    {
        $this->assertTrue(method_exists(BatchService::class, 'createSuccessionPlanBatch'));
    }

    /** @test */
    public function it_has_parallel_candidate_search_method()
    {
        $this->assertTrue(method_exists(BatchService::class, 'parallelCandidateSearch'));
    }

    /** @test */
    public function it_has_process_bulk_plans_method()
    {
        $this->assertTrue(method_exists(BatchService::class, 'processBulkPlans'));
    }

    /** @test */
    public function create_succession_plan_batch_method_has_correct_signature()
    {
        $reflection = new \ReflectionMethod(BatchService::class, 'createSuccessionPlanBatch');
        
        $this->assertTrue($reflection->isPublic());
        $this->assertEquals(2, $reflection->getNumberOfParameters());
    }

    /** @test */
    public function parallel_candidate_search_method_has_correct_signature()
    {
        $reflection = new \ReflectionMethod(BatchService::class, 'parallelCandidateSearch');
        
        $this->assertTrue($reflection->isPublic());
        $this->assertEquals(2, $reflection->getNumberOfParameters());
    }
}
