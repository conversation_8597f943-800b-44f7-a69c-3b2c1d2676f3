<?php

namespace Tests\Unit\Models;

use Tests\TestCase;
use App\Models\SuccessionPlan;

class SimpleSuccessionPlanTest extends TestCase
{
    /** @test */
    public function it_has_correct_fillable_attributes()
    {
        $expectedFillable = [
            'name',
            'description',
            'minimum_Experience',
            'step_up',
            'ethnicity',
            'tagged_individual',
            'status',
            'shared_with',
            'user_id',
            'age',
            'last_opened',
            'candidate_status',
            'mover'
        ];

        $plan = new SuccessionPlan();
        
        $this->assertEquals($expectedFillable, $plan->getFillable());
    }

    /** @test */
    public function it_can_create_instance()
    {
        $plan = new SuccessionPlan([
            'name' => 'Test Plan',
            'description' => 'Test Description',
            'status' => 'pending'
        ]);

        $this->assertInstanceOf(SuccessionPlan::class, $plan);
        $this->assertEquals('Test Plan', $plan->name);
        $this->assertEquals('Test Description', $plan->description);
        $this->assertEquals('pending', $plan->status);
    }

    /** @test */
    public function it_can_set_and_get_attributes()
    {
        $plan = new SuccessionPlan();
        
        $plan->name = 'Senior Developer';
        $plan->minimum_Experience = '5+ years';
        $plan->ethnicity = 'diverse';
        $plan->mover = true;

        $this->assertEquals('Senior Developer', $plan->name);
        $this->assertEquals('5+ years', $plan->minimum_Experience);
        $this->assertEquals('diverse', $plan->ethnicity);
        $this->assertTrue($plan->mover);
    }

    /** @test */
    public function it_can_store_shared_with_data()
    {
        $plan = new SuccessionPlan();
        $plan->shared_with = '<EMAIL>,<EMAIL>';

        $this->assertEquals('<EMAIL>,<EMAIL>', $plan->shared_with);
    }

    /** @test */
    public function it_can_store_tagged_individuals()
    {
        $plan = new SuccessionPlan();
        $plan->tagged_individual = 'John Doe,Jane Smith';

        $this->assertEquals('John Doe,Jane Smith', $plan->tagged_individual);
    }

    /** @test */
    public function it_has_relationship_methods()
    {
        $plan = new SuccessionPlan();

        $this->assertTrue(method_exists($plan, 'successPeople'));
        $this->assertTrue(method_exists($plan, 'user'));
        $this->assertTrue(method_exists($plan, 'requirements'));
    }

    /** @test */
    public function it_has_correct_table_name()
    {
        $plan = new SuccessionPlan();
        
        // Default Laravel convention: snake_case plural
        $this->assertEquals('succession_plans', $plan->getTable());
    }

    /** @test */
    public function it_uses_timestamps()
    {
        $plan = new SuccessionPlan();
        
        $this->assertTrue($plan->usesTimestamps());
    }
}
