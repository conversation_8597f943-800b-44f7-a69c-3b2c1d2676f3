<?php

namespace Tests\Unit\Models;

use Tests\TestCase;
use App\Models\SuccessionPlan;
use App\Models\User;

class SimpleModelTest extends TestCase
{
    /** @test */
    public function succession_plan_has_correct_fillable_attributes()
    {
        $expectedFillable = [
            'name',
            'description',
            'minimum_Experience',
            'step_up',
            'ethnicity',
            'tagged_individual',
            'status',
            'shared_with',
            'user_id',
            'age',
            'last_opened',
            'candidate_status',
            'mover'
        ];

        $plan = new SuccessionPlan();
        
        $this->assertEquals($expectedFillable, $plan->getFillable());
    }

    /** @test */
    public function user_has_correct_fillable_attributes()
    {
        $expectedFillable = [
            'name',
            'team',
            'role',
            'email',
            'password',
            'image_url',
            'company_id',
            'account_id',
            'profile_pic',
            'last_activity'
        ];

        $user = new User();
        
        $this->assertEquals($expectedFillable, $user->getFillable());
    }

    /** @test */
    public function user_hides_sensitive_attributes()
    {
        $expectedHidden = [
            'password',
            'remember_token'
        ];

        $user = new User();
        
        $this->assertEquals($expectedHidden, $user->getHidden());
    }
}
