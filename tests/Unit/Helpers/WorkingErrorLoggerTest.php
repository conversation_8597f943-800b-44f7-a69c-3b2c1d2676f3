<?php

namespace Tests\Unit\Helpers;

use Tests\TestCase;
use App\Services\ErrorHandling\ErrorLogger;
use Illuminate\Support\Facades\Log;
use Exception;

class WorkingErrorLoggerTest extends TestCase
{
    /** @test */
    public function it_has_correct_error_categories()
    {
        $this->assertEquals('api', ErrorLogger::CATEGORY_API);
        $this->assertEquals('database', ErrorLogger::CATEGORY_DATABASE);
        $this->assertEquals('security', ErrorLogger::CATEGORY_SECURITY);
        $this->assertEquals('processing', ErrorLogger::CATEGORY_PROCESSING);
        $this->assertEquals('external', ErrorLogger::CATEGORY_EXTERNAL);
        $this->assertEquals('ui', ErrorLogger::CATEGORY_UI);
    }

    /** @test */
    public function it_can_call_log_method_without_errors()
    {
        Log::shouldReceive('channel')->andReturnSelf();
        Log::shouldReceive('error')->once();

        // This should not throw an exception
        ErrorLogger::log('Test error message');
        
        $this->assertTrue(true);
    }

    /** @test */
    public function it_can_call_log_exception_method_without_errors()
    {
        Log::shouldReceive('channel')->andReturnSelf();
        Log::shouldReceive('error')->once();

        $exception = new Exception('Test exception');
        
        // This should not throw an exception
        ErrorLogger::logException($exception);
        
        $this->assertTrue(true);
    }

    /** @test */
    public function it_accepts_different_categories()
    {
        Log::shouldReceive('channel')->andReturnSelf();
        Log::shouldReceive('error')->once();

        ErrorLogger::log('API error', [], ErrorLogger::CATEGORY_API);
        
        $this->assertTrue(true);
    }

    /** @test */
    public function it_accepts_different_severity_levels()
    {
        Log::shouldReceive('channel')->andReturnSelf();
        Log::shouldReceive('warning')->once();

        ErrorLogger::log('Warning message', [], ErrorLogger::CATEGORY_PROCESSING, 'warning');
        
        $this->assertTrue(true);
    }

    /** @test */
    public function it_accepts_context_data()
    {
        Log::shouldReceive('channel')->andReturnSelf();
        Log::shouldReceive('error')->once();

        $context = ['user_id' => 123, 'action' => 'login'];
        ErrorLogger::log('Login failed', $context, ErrorLogger::CATEGORY_SECURITY);
        
        $this->assertTrue(true);
    }
}
