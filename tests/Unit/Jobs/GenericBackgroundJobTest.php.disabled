<?php

namespace Tests\Unit\Jobs;

use Tests\TestCase;
use App\Jobs\GenericBackgroundJob;
use App\Models\BackgroundProcess;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Mockery;
use Exception;
use InvalidArgumentException;

class GenericBackgroundJobTest extends TestCase
{
    use DatabaseTransactions;

    protected $testClass;
    protected $testMethod;
    protected $testParameters;
    protected $backgroundProcess;

    protected function setUp(): void
    {
        parent::setUp();

        $this->testClass = TestServiceClass::class;
        $this->testMethod = 'testMethod';
        $this->testParameters = ['param1' => 'value1', 'param2' => 'value2'];

        // Create test background process
        $this->backgroundProcess = BackgroundProcess::create([
            'class_name' => $this->testClass,
            'method_name' => $this->testMethod,
            'parameters' => json_encode($this->testParameters),
            'status' => 'pending'
        ]);
    }

    /** @test */
    public function it_constructs_with_correct_properties()
    {
        $job = new GenericBackgroundJob(
            $this->testClass,
            $this->testMethod,
            $this->testParameters,
            $this->backgroundProcess->id
        );

        $reflection = new \ReflectionClass($job);
        
        $classProperty = $reflection->getProperty('class');
        $classProperty->setAccessible(true);
        $this->assertEquals($this->testClass, $classProperty->getValue($job));

        $methodProperty = $reflection->getProperty('method');
        $methodProperty->setAccessible(true);
        $this->assertEquals($this->testMethod, $methodProperty->getValue($job));

        $parametersProperty = $reflection->getProperty('parameters');
        $parametersProperty->setAccessible(true);
        $this->assertEquals($this->testParameters, $parametersProperty->getValue($job));

        $processIdProperty = $reflection->getProperty('processId');
        $processIdProperty->setAccessible(true);
        $this->assertEquals($this->backgroundProcess->id, $processIdProperty->getValue($job));

        // Test timeout and tries are set correctly
        $this->assertEquals(1800, $job->timeout);
        $this->assertEquals(3, $job->tries);
    }

    /** @test */
    public function it_skips_when_process_not_found()
    {
        Log::spy();

        $job = new GenericBackgroundJob(
            $this->testClass,
            $this->testMethod,
            $this->testParameters,
            999 // Non-existent process ID
        );

        $job->handle();

        Log::shouldHaveReceived('warning')
            ->once()
            ->with('GENERIC JOB: Process 999 not found, skipping job', [
                'class' => $this->testClass,
                'method' => $this->testMethod
            ]);
    }

    /** @test */
    public function it_implements_idempotent_behavior_for_completed_process()
    {
        // Mark process as completed
        $this->backgroundProcess->update(['status' => 'completed']);

        Log::spy();

        $job = new GenericBackgroundJob(
            $this->testClass,
            $this->testMethod,
            $this->testParameters,
            $this->backgroundProcess->id
        );

        $job->handle();

        Log::shouldHaveReceived('info')
            ->once()
            ->with("GENERIC JOB: Process {$this->backgroundProcess->id} already completed, skipping", [
                'class' => $this->testClass,
                'method' => $this->testMethod
            ]);
    }

    /** @test */
    public function it_throws_exception_for_non_existent_class()
    {
        $job = new GenericBackgroundJob(
            'NonExistentClass',
            $this->testMethod,
            $this->testParameters,
            $this->backgroundProcess->id
        );

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Class does not exist: NonExistentClass');

        try {
            $job->handle();
            $this->fail('Expected InvalidArgumentException was not thrown');
        } catch (InvalidArgumentException $e) {
            // Check process was marked as failed
            $this->backgroundProcess->refresh();
            $this->assertEquals('failed', $this->backgroundProcess->status);
            $this->assertStringContainsString('Class does not exist: NonExistentClass', $this->backgroundProcess->output);
            throw $e;
        }
    }

    /** @test */
    public function it_throws_exception_for_non_existent_method()
    {
        $methodName = 'nonExistentMethod';
        $job = new GenericBackgroundJob(
            $this->testClass,
            $methodName,
            $this->testParameters,
            $this->backgroundProcess->id
        );

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage("Method does not exist: {$this->testClass}::{$methodName}");

        try {
            $job->handle();
            $this->fail('Expected InvalidArgumentException was not thrown');
        } catch (InvalidArgumentException $e) {
            // Check process was marked as failed
            $this->backgroundProcess->refresh();
            $this->assertEquals('failed', $this->backgroundProcess->status);
            $this->assertStringContainsString("Method does not exist: {$this->testClass}::{$methodName}", $this->backgroundProcess->output);
            throw $e;
        }
    }

    /** @test */
    public function it_executes_successful_method_call()
    {
        // Create a test class that we can actually call
        $testClass = TestServiceClass::class;
        $methodName = 'successfulMethod';
        $input = 'test';
        $expectedOutput = ['result' => 'success', 'input' => $input];
        
        // Create a fresh process for this test
        $process = BackgroundProcess::create([
            'class_name' => $testClass,
            'method_name' => $methodName,
            'parameters' => json_encode(['input' => $input]),
            'status' => 'pending'
        ]);
        
        $job = new GenericBackgroundJob(
            $testClass,
            $methodName,
            ['input' => $input],
            $process->id
        );
        
        // Execute the job
        $job->handle();

        // Refresh the process from the database
        $process->refresh();
        
        // Check process was marked as completed
        $this->assertEquals('completed', $process->status);
        
        // Verify the output matches expected result
        $output = json_decode($process->output, true);
        $this->assertEquals($expectedOutput, $output);
        
        // Verify the process was updated in the database
        $this->assertDatabaseHas('background_processes', [
            'id' => $process->id,
            'status' => 'completed',
            'output' => json_encode($expectedOutput),
        ]);
        
        // Clean up
        $process->delete();
    }

    /** @test */
    public function it_handles_method_exceptions_properly()
    {
        $testClass = TestServiceClass::class;
        $methodName = 'failingMethod';
        $errorMessage = 'Method failed intentionally';
        
        // Create a new process for this test to avoid conflicts
        $process = BackgroundProcess::create([
            'class_name' => $testClass,
            'method_name' => $methodName,
            'parameters' => json_encode([]),
            'status' => 'pending'
        ]);
        
        $job = new GenericBackgroundJob(
            $testClass,
            $methodName,
            [],
            $process->id
        );

        $this->expectException(Exception::class);
        $this->expectExceptionMessage($errorMessage);

        try {
            $job->handle();
            $this->fail('Expected Exception was not thrown');
        } catch (Exception $e) {
            // Check process was marked as failed
            $process->refresh();
            $this->assertEquals('failed', $process->status);
            $this->assertStringContainsString($errorMessage, $process->output);
            throw $e;
        } finally {
            // Clean up
            $process->delete();
        }
    }

    /** @test */
    public function it_updates_process_to_running_before_execution()
    {
        $testClass = TestServiceClass::class;
        $methodName = 'successfulMethod';
        $input = 'test';
        
        // Create a fresh process for this test
        $process = BackgroundProcess::create([
            'class_name' => $testClass,
            'method_name' => $methodName,
            'parameters' => json_encode(['input' => $input]),
            'status' => 'pending'
        ]);
        
        // Fake the logger to capture log messages
        Log::shouldReceive('info')
            ->withArgs(function ($message, $context) use ($testClass, $methodName) {
                // Check if this is the log message we're interested in
                if (str_contains($message, 'GENERIC JOB: Starting background process')) {
                    $this->assertEquals($testClass, $context['class']);
                    $this->assertEquals($methodName, $context['method']);
                    return true;
                }
                return false;
            })
            ->atLeast()->once();
        
        $job = new GenericBackgroundJob(
            $testClass,
            $methodName,
            ['input' => $input],
            $process->id
        );
        
        // Execute the job
        $job->handle();

        // Refresh the process from the database
        $process->refresh();
        
        // Check the final state is completed
        $this->assertEquals('completed', $process->status);
        
        // Verify the process was updated with the correct output
        $output = json_decode($process->output, true);
        $this->assertEquals(['result' => 'success', 'input' => $input], $output);
        
        // Clean up
        $process->delete();
    }

    /** @test */
    public function it_logs_failure_properly_in_failed_method()
    {
        Log::spy();

        $exception = new Exception('Test failure');
        $job = new GenericBackgroundJob(
            $this->testClass,
            $this->testMethod,
            $this->testParameters,
            $this->backgroundProcess->id
        );

        $job->failed($exception);

        Log::shouldHaveReceived('error')
            ->once()
            ->with('GENERIC JOB: Background process failed after all retry attempts', Mockery::on(function ($data) use ($exception) {
                return $data['job'] === GenericBackgroundJob::class
                    && $data['process_id'] === $this->backgroundProcess->id
                    && $data['class'] === $this->testClass
                    && $data['method'] === $this->testMethod
                    && $data['parameters'] === $this->testParameters
                    && $data['exception'] === 'Test failure'
                    && $data['max_attempts'] === 3
                    && isset($data['backoff_delays'])
                    && isset($data['retry_until']);
            }));
    }

    /** @test */
    public function it_updates_process_record_on_final_failure()
    {
        // Create a fresh process for this test
        $process = BackgroundProcess::create([
            'class_name' => $this->testClass,
            'method_name' => $this->testMethod,
            'parameters' => json_encode($this->testParameters),
            'status' => 'pending'
        ]);
        
        $exception = new Exception('Final failure');
        $job = new GenericBackgroundJob(
            $this->testClass,
            $this->testMethod,
            $this->testParameters,
            $process->id
        );

        // Set the number of attempts that have been made
        $reflection = new \ReflectionClass($job);
        $attemptsProperty = $reflection->getProperty('attempts');
        $attemptsProperty->setAccessible(true);
        $attemptsProperty->setValue($job, 3); // Max retries reached
        
        // Mock the logger to verify the error is logged
        Log::spy();
        
        try {
            // Execute the failed method
            $job->failed($exception);
            
            // Refresh the process from the database
            $process->refresh();
            
            // Verify the process was marked as failed
            $this->assertEquals('failed', $process->status);
            
            // Check the error message contains the expected text
            $this->assertStringContainsString('Job failed after 3 attempts', $process->output);
            $this->assertStringContainsString('Final failure', $process->output);
            
            // Verify the failed_at timestamp was set
            $this->assertNotNull($process->failed_at);
            
            // Verify the error was logged
            Log::shouldHaveReceived('error')
                ->once()
                ->with(
                    'GENERIC JOB: Background process failed after all retry attempts',
                    Mockery::on(function ($context) use ($process, $exception) {
                        return $context['process_id'] === $process->id &&
                               $context['class'] === $this->testClass &&
                               $context['method'] === $this->testMethod &&
                               $context['exception'] === $exception->getMessage() &&
                               $context['max_attempts'] === 3;
                    })
                );
        } finally {
            // Clean up
            $process->delete();
        }
    }

    /** @test */
    public function it_handles_process_update_failure_gracefully()
    {
        // Set up test data
        $processId = $this->backgroundProcess->id;
        $errorMessage = 'Test failure';
        $exception = new Exception($errorMessage);
        
        // Create the job with the process ID
        $job = new GenericBackgroundJob(
            $this->testClass,
            $this->testMethod,
            $this->testParameters,
            $processId
        );
        
        // Set the number of attempts
        $job->attempts = 2;
        
        // Mock the logger to verify the warning is logged
        Log::spy();
        
        // Delete the process to simulate a missing record
        $this->backgroundProcess->delete();
        
        // Execute the failed method
        $job->failed($exception);
        
        // Verify the warning was logged with the expected message
        Log::shouldHaveReceived('warning')
            ->once()
            ->with(
                'GENERIC JOB: Failed to update process record after job failure',
                Mockery::on(function ($context) use ($processId, $errorMessage) {
                    return $context['process_id'] === $processId &&
                           str_contains($context['error'] ?? '', $errorMessage);
                })
            );
            
        // Verify the error was still logged even though the process record couldn't be updated
        Log::shouldHaveReceived('error')
            ->once()
            ->with(
                'GENERIC JOB: Background process failed after all retry attempts',
                Mockery::on(function ($context) use ($processId, $errorMessage) {
                    return $context['process_id'] === $processId &&
                           $context['exception'] === $errorMessage;
                })
            );
    }

    /** @test */
    public function it_configures_exponential_backoff_properly()
    {
        $job = new GenericBackgroundJob(
            $this->testClass,
            $this->testMethod,
            $this->testParameters,
            $this->backgroundProcess->id
        );

        // Test backoff configuration through reflection
        $reflection = new \ReflectionClass($job);
        
        $baseDelayProperty = $reflection->getProperty('baseDelay');
        $baseDelayProperty->setAccessible(true);
        $this->assertEquals(30, $baseDelayProperty->getValue($job));

        $maxDelayProperty = $reflection->getProperty('maxDelay');
        $maxDelayProperty->setAccessible(true);
        $this->assertEquals(900, $maxDelayProperty->getValue($job));
    }

    /** @test */
    public function it_sets_correct_retry_until_timestamp()
    {
        $job = new GenericBackgroundJob(
            $this->testClass,
            $this->testMethod,
            $this->testParameters,
            $this->backgroundProcess->id
        );

        $retryUntil = $job->retryUntil();

        // Should be 2 hours from now (allowing some tolerance for test execution time)
        $expectedTime = now()->addHours(2);
        $this->assertTrue($retryUntil->diffInMinutes($expectedTime) < 1);
    }

    /** @test */
    public function it_can_be_dispatched_to_queue()
    {
        Queue::fake();

        GenericBackgroundJob::dispatch(
            $this->testClass,
            $this->testMethod,
            $this->testParameters,
            $this->backgroundProcess->id
        );

        Queue::assertPushed(GenericBackgroundJob::class);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}

/**
 * Test service class for testing GenericBackgroundJob
 */
class TestServiceClass
{
    public static function successfulMethod($input = null)
    {
        return ['result' => 'success', 'input' => $input];
    }

    public static function failingMethod()
    {
        throw new Exception('Method failed intentionally');
    }

    public static function testMethod()
    {
        return ['status' => 'success'];
    }
    
    public static function __callStatic($name, $arguments)
    {
        if ($name === 'failingMethod') {
            throw new Exception('Method failed intentionally');
        }
        
        if ($name === 'successfulMethod') {
            return ['result' => 'success', 'input' => $arguments[0] ?? null];
        }
        
        throw new BadMethodCallException("Method {$name} does not exist");
    }
}