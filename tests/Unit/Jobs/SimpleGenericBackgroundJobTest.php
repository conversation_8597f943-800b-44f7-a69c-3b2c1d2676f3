<?php

namespace Tests\Unit\Jobs;

use Tests\TestCase;
use App\Jobs\GenericBackgroundJob;
use Illuminate\Support\Facades\Queue;
use Carbon\Carbon;

class SimpleGenericBackgroundJobTest extends TestCase
{
    /** @test */
    public function it_can_be_instantiated_with_correct_properties()
    {
        $className = 'TestService';
        $methodName = 'testMethod';
        $parameters = ['param1' => 'value1'];
        $processId = 1;
        
        $job = new GenericBackgroundJob($className, $methodName, $parameters, $processId);
        
        $this->assertInstanceOf(GenericBackgroundJob::class, $job);
    }

    /** @test */
    public function it_configures_exponential_backoff_properly()
    {
        $job = new GenericBackgroundJob('TestService', 'testMethod', [], 1);
        
        $this->assertEquals(3, $job->tries);
        $this->assertEquals(1800, $job->timeout);
    }

    /** @test */
    public function it_sets_correct_retry_until_timestamp()
    {
        $job = new GenericBackgroundJob('TestService', 'testMethod', [], 1);
        
        $retryUntil = $job->retryUntil();
        $expectedTime = now()->addHours(2);
        
        $this->assertInstanceOf(Carbon::class, $retryUntil);
        $this->assertTrue($retryUntil->diffInMinutes($expectedTime) < 1);
    }

    /** @test */
    public function it_can_be_dispatched_to_queue()
    {
        Queue::fake();
        
        GenericBackgroundJob::dispatch('TestService', 'testMethod', ['param' => 'value'], 1);
        
        Queue::assertPushed(GenericBackgroundJob::class);
    }

    /** @test */
    public function it_has_correct_queue_configuration()
    {
        $job = new GenericBackgroundJob('TestService', 'testMethod', [], 1);
        
        // Test that job can have queue set
        $this->assertNull($job->queue); // Default is null unless explicitly set
    }
}
