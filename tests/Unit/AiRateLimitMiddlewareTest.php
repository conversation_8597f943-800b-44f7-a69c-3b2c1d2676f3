<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Http\Middleware\AiRateLimit;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;

class AiRateLimitMiddlewareTest extends TestCase
{
    protected $middleware;

    protected function setUp(): void
    {
        parent::setUp();
        $this->middleware = new AiRateLimit();
    }

    /** @test */
    public function middleware_allows_requests_within_limit()
    {
        $user = new User(['id' => 1, 'role' => 'user']);
        $request = Request::create('/ai-chat/send-message', 'POST');
        $request->setUserResolver(function () use ($user) {
            return $user;
        });

        RateLimiter::clear('openai:1');

        $response = $this->middleware->handle($request, function ($req) {
            return new Response('OK', 200);
        }, 'openai');

        $this->assertEquals(200, $response->getStatusCode());
    }

    /** @test */
    public function middleware_blocks_requests_over_limit()
    {
        $user = new User(['id' => 2, 'role' => 'user']);
        $request = Request::create('/ai-chat/send-message', 'POST');
        $request->setUserResolver(function () use ($user) {
            return $user;
        });

        // Test middleware can handle rate limiting by checking if it sets headers
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('OK', 200);
        }, 'openai');

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertNotNull($response->headers->get('X-RateLimit-Limit'));
        $this->assertNotNull($response->headers->get('X-RateLimit-Remaining'));
        $this->assertNotNull($response->headers->get('X-RateLimit-Reset'));
    }

    /** @test */
    public function middleware_allows_unlimited_requests_for_admins()
    {
        $admin = new User(['id' => 3, 'role' => 'admin']);
        $request = Request::create('/ai-chat/send-message', 'POST');
        $request->setUserResolver(function () use ($admin) {
            return $admin;
        });

        $response = $this->middleware->handle($request, function ($req) {
            return new Response('OK', 200);
        }, 'openai');

        $this->assertEquals(200, $response->getStatusCode());
    }

    /** @test */
    public function middleware_increments_attempts_for_regular_users()
    {
        $user = new User(['id' => 4, 'role' => 'user']);
        $request = Request::create('/ai-chat/send-message', 'POST');
        $request->setUserResolver(function () use ($user) {
            return $user;
        });

        $response = $this->middleware->handle($request, function ($req) {
            return new Response('OK', 200);
        }, 'openai');

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('4500', $response->headers->get('X-RateLimit-Limit'));
        $this->assertNotNull($response->headers->get('X-RateLimit-Reset'));
    }

    /** @test */
    public function middleware_handles_different_ai_services()
    {
        $user = new User(['id' => 5, 'role' => 'user']);
        $request = Request::create('/talentpoolai/send-message', 'POST');
        $request->setUserResolver(function () use ($user) {
            return $user;
        });

        RateLimiter::clear('anthropic:5');

        $response = $this->middleware->handle($request, function ($req) {
            return new Response('OK', 200);
        }, 'anthropic');

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('900', $response->headers->get('X-RateLimit-Limit'));
        $this->assertNotNull($response->headers->get('X-RateLimit-Reset'));
    }
}