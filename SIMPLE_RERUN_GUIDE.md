# Simple Plan Rerun Guide

## Overview
This command allows you to rerun searches for any existing succession plan using the stored plan data.

## Usage

### Basic Usage
Rerun both internal and external searches for a plan:
```bash
php artisan plan:rerun 608
```

### Search Types
Run only external search:
```bash
php artisan plan:rerun 608 --type=external
```

Run only internal search:
```bash
php artisan plan:rerun 608 --type=internal
```

### View Plan Info
Check plan details without running:
```bash
php artisan plan:rerun 608 --info
```

## How It Works

1. The command loads the existing succession plan from the database
2. Reconstructs the plan data from stored fields
3. Dispatches the same SearchCandidatesJob with original parameters
4. New candidates are added to the existing plan

## Examples

```bash
# Rerun plan 608 with both searches
php artisan plan:rerun 608

# Check what will be searched for plan 608
php artisan plan:rerun 608 --info

# Run only external search for plan 608
php artisan plan:rerun 608 --type=external
```

## What Gets Rerun

The command uses these stored values from the plan:
- Target roles
- Companies
- Countries/Locations
- Minimum tenure
- Gender preferences
- Alumni inclusion
- Skills and qualifications
- All other search criteria

## Benefits

- No need to store conversation traces
- Uses existing plan data
- Simple one-line command
- Can run internal/external separately
- View plan details before rerunning

## Finding Plan IDs

To find plan IDs, you can:
1. Check the Plans page in the web interface
2. Look in the database: `SELECT id, plan_name FROM succession_plans ORDER BY created_at DESC;`
3. Check URLs when viewing plans (e.g., `/plans/show/608`)

That's it! One simple command to rerun any plan's searches.