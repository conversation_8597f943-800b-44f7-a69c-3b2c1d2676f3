name: Deploy to Staging

on:
  push:
    branches: [ staging ]
  workflow_dispatch:
    inputs:
      skip_tests:
        description: 'Skip automated tests'
        required: false
        default: 'false'
        type: boolean

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: staging
    
    steps:
    - name: Deploy to Staging Server
      run: |
        echo "🚀 Starting Staging Deployment..."
        
        # Create SSH directory and set permissions
        mkdir -p ~/.ssh
        chmod 700 ~/.ssh
        
        # Decode and save the EC2 private key
        echo "${{ secrets.STAGING_SSH_PRIVATE_KEY }}" | base64 -d > ~/.ssh/ec2-key
        chmod 600 ~/.ssh/ec2-key
        
        # Add staging EC2 to known hosts
        ssh-keyscan -H ${{ secrets.STAGING_HOST }} >> ~/.ssh/known_hosts
        
        # Set deployment variables
        export SKIP_TESTS="${{ github.event.inputs.skip_tests }}"
        export GITHUB_SHA="${{ github.sha }}"
        export GITHUB_REF="${{ github.ref }}"
        
        # SSH into the server and deploy
        ssh -o StrictHostKeyChecking=no -i ~/.ssh/ec2-key ${{ secrets.STAGING_USER }}@${{ secrets.STAGING_HOST }} "bash -s" <<'EOF'
        set -e
        
        # Configuration
        export DEPLOY_ENV="staging"
        export APP_ROOT="/var/www/successionplanai"
        export SHARED_DIR="$APP_ROOT/shared"
        export BACKUP_DIR="/var/backups/successionplanai-staging"
        export ROLLBACK_RELEASES=5
        export GITHUB_TOKEN="${{ secrets.GH_PAT }}"
        export SKIP_TESTS="$SKIP_TESTS"
        
        # Colors for output
        RED='\033[0;31m'
        GREEN='\033[0;32m'
        YELLOW='\033[1;33m'
        BLUE='\033[0;34m'
        NC='\033[0m'
        
        log() {
            echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
        }
        
        log_success() {
            echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] ✓${NC} $1"
        }
        
        log_error() {
            echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ✗${NC} $1"
        }
        
        # Error handling
        cleanup_on_error() {
            log_error "Deployment failed! Starting rollback..."
            
            if [[ -d "$APP_ROOT/releases" ]]; then
                local previous_release=$(ls -t "$APP_ROOT/releases" | sed -n '2p')
                if [[ -n "$previous_release" ]]; then
                    log "Rolling back to: $previous_release"
                    ln -nfs "$APP_ROOT/releases/$previous_release" "$APP_ROOT/current"
                    
                    # Restart services
                    sudo systemctl reload php8.3-fpm
                    sudo systemctl reload nginx
                    
                    log_success "Rollback completed"
                fi
            fi
            exit 1
        }
        
        trap cleanup_on_error ERR
        
        log "🚀 Starting SuccessionPlan AI Staging Deployment"
        log "Environment: $DEPLOY_ENV"
        log "Commit: $GITHUB_SHA"
        log "Branch: $GITHUB_REF"
        
        # Pre-deployment checks
        log "Running pre-deployment checks..."
        
        # Check disk space (3GB minimum for staging)
        available_space=$(df "$APP_ROOT" | awk 'NR==2 {print $4}')
        if [[ $available_space -lt 3145728 ]]; then
            log_error "Insufficient disk space"
            exit 1
        fi
        
        # Check services
        for service in nginx php8.3-fpm mysql redis-server; do
            if ! systemctl is-active --quiet "$service"; then
                log_error "Service $service is not running"
                exit 1
            fi
        done
        
        log_success "Pre-deployment checks passed"
        
        # Setup deployment structure
        sudo mkdir -p "$APP_ROOT"/{releases,shared}
        sudo mkdir -p "$SHARED_DIR"/{storage,uploads}
        sudo mkdir -p "$BACKUP_DIR"
        
        # Create new release
        release_timestamp=$(date +"%Y%m%d_%H%M%S")
        release_path="$APP_ROOT/releases/$release_timestamp"
        
        log "Creating release: $release_timestamp"
        
        # Clone repository
        git clone --depth 1 --branch staging \
            https://$<EMAIL>/SuccessionplanAI/successionplan-ai.git \
            "$release_path"
        
        cd "$release_path"
        
        # Set ownership
        sudo chown -R ubuntu:www-data "$release_path"
        
        # Install Composer dependencies
        log "Installing Composer dependencies..."
        composer install --no-interaction --prefer-dist --no-dev --optimize-autoloader
        
        # Install Node.js dependencies
        log "Installing Node.js dependencies..."
        npm install --legacy-peer-deps --production
        npm install @inertiajs/inertia sweetalert2 react-window
        
        # Install additional Composer packages
        composer require rap2hpoutre/fast-excel phpoffice/phpword --no-interaction
        
        # Setup shared resources
        log "Setting up shared resources..."
        
        # Link shared storage
        rm -rf storage
        ln -nfs "$SHARED_DIR/storage" storage
        
        # Link .env file
        if [[ -f "$SHARED_DIR/.env" ]]; then
            rm -f .env
            ln -nfs "$SHARED_DIR/.env" .env
        else
            log_error "Staging .env file not found"
            exit 1
        fi
        
        # Ensure shared directories exist
        sudo mkdir -p "$SHARED_DIR/storage"/{app,framework,logs}
        sudo mkdir -p "$SHARED_DIR/storage/framework"/{cache,sessions,views}
        sudo mkdir -p "$SHARED_DIR/storage/app"/{public,uploads}
        sudo chown -R www-data:www-data "$SHARED_DIR/storage"
        sudo chmod -R 775 "$SHARED_DIR/storage"
        
        # Generate app key if needed
        if ! grep -q "APP_KEY=base64:" .env; then
            php artisan key:generate --force
        fi
        
        # Build frontend assets
        log "Building frontend assets..."
        npm run build
        
        # Cache Laravel configuration
        php artisan config:cache
        php artisan route:cache
        php artisan view:cache
        
        # Set permissions
        sudo chown -R ubuntu:www-data "$release_path"
        sudo chmod -R 755 "$release_path"
        sudo chmod -R 775 "$release_path/storage" "$release_path/bootstrap/cache"
        
        # Create storage link
        if [[ ! -L "public/storage" ]]; then
            php artisan storage:link
        fi
        
        # Run database migrations
        log "Running database migrations..."
        php artisan migrate --force
        
        # Run tests if not skipped
        if [[ "$SKIP_TESTS" != "true" ]] && [[ -f "phpunit.xml" ]]; then
            log "Running automated tests..."
            if ! vendor/bin/phpunit --testsuite=Feature --stop-on-failure; then
                log_error "Tests failed"
                exit 1
            fi
            log_success "Tests passed"
        fi
        
        # Health checks
        log "Running health checks..."
        
        if ! php artisan about > /dev/null 2>&1; then
            log_error "Laravel health check failed"
            exit 1
        fi
        
        if ! php artisan tinker --execute="DB::connection()->getPdo();" > /dev/null 2>&1; then
            log_error "Database check failed"
            exit 1
        fi
        
        log_success "Health checks passed"
        
        # Switch to new release (atomic)
        log "Switching to new release..."
        temp_link="$APP_ROOT/current_tmp_$(date +%s)"
        ln -nfs "$release_path" "$temp_link"
        mv "$temp_link" "$APP_ROOT/current"
        
        # Restart services
        log "Restarting services..."
        sudo systemctl reload php8.3-fpm
        sudo systemctl reload nginx
        
        # Clear OPcache
        php -r "if(function_exists('opcache_reset')) opcache_reset();"
        
        # Start queue services
        cd "$APP_ROOT/current"
        
        # Stop existing Horizon
        php artisan horizon:terminate || true
        sleep 5
        
        # Start Horizon
        nohup php artisan horizon > "$SHARED_DIR/storage/logs/horizon.log" 2>&1 &
        sleep 10
        
        # Verify Horizon
        horizon_status=$(php artisan horizon:status 2>/dev/null || echo "inactive")
        if [[ "$horizon_status" == "running" ]]; then
            log_success "Horizon started successfully"
        else
            log_error "Horizon failed to start"
        fi
        
        # Cleanup old releases (keep last 5)
        cd "$APP_ROOT/releases"
        releases_to_remove=$(ls -t | tail -n +6)
        if [[ -n "$releases_to_remove" ]]; then
            echo "$releases_to_remove" | xargs rm -rf
            log_success "Cleaned up old releases"
        fi
        
        # Final verification
        sleep 15
        cd "$APP_ROOT/current"
        
        if php artisan about > /dev/null 2>&1; then
            log_success "Final verification passed"
        else
            log_error "Final verification failed"
            exit 1
        fi
        
        log_success "=========================================="
        log_success "STAGING DEPLOYMENT COMPLETED SUCCESSFULLY"
        log_success "=========================================="
        log_success "Release: $release_timestamp"
        log_success "Horizon: $(php artisan horizon:status)"
        log_success "Time: $(date)"
        
        # Send notification
        php artisan queue:notification "✅ STAGING DEPLOYMENT SUCCESS: Release $release_timestamp deployed successfully" --level="success"
        
        EOF
        
        echo "✅ Staging deployment completed successfully!"

    - name: Notify on Failure
      if: failure()
      run: |
        echo "❌ Staging deployment failed"
        # Additional failure notifications can be added here
