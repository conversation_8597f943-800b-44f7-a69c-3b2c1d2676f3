name: Deploy to Production

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      maintenance_window:
        description: 'Deploy during maintenance window (no downtime protection)'
        required: false
        default: 'false'
        type: boolean
      skip_tests:
        description: 'Skip automated tests (NOT RECOMMENDED)'
        required: false
        default: 'false'
        type: boolean
      force_deploy:
        description: 'Force deployment without confirmation'
        required: false
        default: 'false'
        type: boolean

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: production
    
    steps:
    - name: Production Deployment Confirmation
      if: github.event_name == 'push' || github.event.inputs.force_deploy != 'true'
      run: |
        echo "🚨 PRODUCTION DEPLOYMENT INITIATED"
        echo "=========================================="
        echo "This will deploy to the LIVE production server"
        echo "Branch: ${{ github.ref }}"
        echo "Commit: ${{ github.sha }}"
        echo "Triggered by: ${{ github.event_name }}"
        echo "Actor: ${{ github.actor }}"
        echo "=========================================="
        
        # Add a delay for manual cancellation if needed
        echo "⏰ 30-second safety delay - cancel now if this deployment is not intended"
        sleep 30
        echo "✅ Proceeding with production deployment..."

    - name: Deploy to Production Server
      run: |
        echo "🚀 Starting Production Deployment..."
        
        # Create SSH directory and set permissions
        mkdir -p ~/.ssh
        chmod 700 ~/.ssh
        
        # Decode and save the production private key
        echo "${{ secrets.PRODUCTION_SSH_PRIVATE_KEY }}" | base64 -d > ~/.ssh/prod-key
        chmod 600 ~/.ssh/prod-key
        
        # Add production server to known hosts
        ssh-keyscan -H ${{ secrets.PRODUCTION_HOST }} >> ~/.ssh/known_hosts
        
        # Set deployment variables
        export MAINTENANCE_WINDOW="${{ github.event.inputs.maintenance_window }}"
        export SKIP_TESTS="${{ github.event.inputs.skip_tests }}"
        export GITHUB_SHA="${{ github.sha }}"
        export GITHUB_REF="${{ github.ref }}"
        export GITHUB_ACTOR="${{ github.actor }}"
        
        # SSH into production server and deploy
        ssh -o StrictHostKeyChecking=no -i ~/.ssh/prod-key ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }} "bash -s" <<'EOF'
        set -e
        
        # Production Configuration
        export DEPLOY_ENV="production"
        export APP_ROOT="/var/www/successionplanai"
        export SHARED_DIR="$APP_ROOT/shared"
        export BACKUP_DIR="/var/backups/successionplanai-production"
        export ROLLBACK_RELEASES=10
        export GITHUB_TOKEN="${{ secrets.GH_PAT }}"
        export MAINTENANCE_WINDOW="$MAINTENANCE_WINDOW"
        export SKIP_TESTS="$SKIP_TESTS"
        export MAX_DEPLOYMENT_TIME=2400
        export HEALTH_CHECK_TIMEOUT=600
        
        # Colors for output
        RED='\033[0;31m'
        GREEN='\033[0;32m'
        YELLOW='\033[1;33m'
        BLUE='\033[0;34m'
        PURPLE='\033[0;35m'
        NC='\033[0m'
        
        log() {
            echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
        }
        
        log_success() {
            echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] ✓${NC} $1"
        }
        
        log_warning() {
            echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] ⚠${NC} $1"
        }
        
        log_error() {
            echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ✗${NC} $1"
        }
        
        log_critical() {
            echo -e "${PURPLE}[$(date '+%Y-%m-%d %H:%M:%S')] 🚨${NC} $1"
        }
        
        # Enhanced error handling for production
        cleanup_on_error() {
            log_critical "PRODUCTION DEPLOYMENT FAILED! Initiating emergency rollback..."
            
            # Stop maintenance mode if enabled
            if [[ -f "$APP_ROOT/current/storage/framework/maintenance.php" ]]; then
                cd "$APP_ROOT/current" && php artisan up
            fi
            
            # Emergency rollback
            if [[ -d "$APP_ROOT/releases" ]]; then
                local previous_release=$(ls -t "$APP_ROOT/releases" | sed -n '2p')
                if [[ -n "$previous_release" ]]; then
                    log "Emergency rollback to: $previous_release"
                    
                    # Atomic symlink switch
                    temp_link="$APP_ROOT/rollback_tmp_$(date +%s)"
                    ln -nfs "$APP_ROOT/releases/$previous_release" "$temp_link"
                    mv "$temp_link" "$APP_ROOT/current"
                    
                    # Restart services
                    sudo systemctl reload php8.3-fpm
                    sudo systemctl reload nginx
                    
                    # Restart queues
                    cd "$APP_ROOT/current"
                    php artisan horizon:terminate || true
                    sleep 5
                    nohup php artisan horizon > "$SHARED_DIR/storage/logs/horizon.log" 2>&1 &
                    
                    # Verify rollback
                    sleep 30
                    if php artisan about > /dev/null 2>&1; then
                        log_success "Emergency rollback completed"
                        php artisan queue:notification "🚨 PRODUCTION ROLLBACK SUCCESS: Emergency rollback to $previous_release completed" --level="critical"
                    else
                        log_critical "ROLLBACK FAILED - Manual intervention required"
                        php artisan queue:notification "🚨 PRODUCTION ROLLBACK FAILED: Manual intervention required immediately" --level="critical"
                    fi
                else
                    log_critical "No previous release for rollback - manual intervention required"
                fi
            fi
            exit 1
        }
        
        trap cleanup_on_error ERR
        
        # Start production deployment
        start_time=$(date +%s)
        
        log_critical "=========================================="
        log_critical "STARTING PRODUCTION DEPLOYMENT"
        log_critical "=========================================="
        log "Environment: $DEPLOY_ENV"
        log "Commit: $GITHUB_SHA"
        log "Branch: $GITHUB_REF"
        log "Deployed by: $GITHUB_ACTOR"
        log "Started at: $(date)"
        
        # Enhanced pre-deployment checks for production
        log "Running comprehensive production checks..."
        
        # System resource checks
        available_space=$(df "$APP_ROOT" | awk 'NR==2 {print $4}')
        if [[ $available_space -lt 5242880 ]]; then  # 5GB
            log_error "Insufficient disk space (need 5GB, have $(($available_space/1024/1024))GB)"
            exit 1
        fi
        
        available_memory=$(free -m | awk 'NR==2{print $7}')
        if [[ $available_memory -lt 1024 ]]; then
            log_warning "Low memory: ${available_memory}MB"
        fi
        
        cpu_load=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
        if (( $(echo "$cpu_load > 2.0" | bc -l) )); then
            log_warning "High CPU load: $cpu_load"
        fi
        
        # Critical service checks
        for service in nginx php8.3-fpm mysql redis-server; do
            if ! systemctl is-active --quiet "$service"; then
                log_error "Critical service $service is not running"
                exit 1
            fi
        done
        
        # Database performance check
        db_response=$(cd "$APP_ROOT/current" && timeout 10 php artisan tinker --execute="
            \$start = microtime(true);
            DB::connection()->getPdo();
            DB::select('SELECT 1');
            echo round((microtime(true) - \$start) * 1000, 2);
        " 2>/dev/null || echo "timeout")
        
        if [[ "$db_response" == "timeout" ]] || (( $(echo "$db_response > 1000" | bc -l) )); then
            log_error "Database too slow: ${db_response}ms"
            exit 1
        fi
        
        # Redis performance check
        redis_response=$(timeout 5 bash -c 'start=$(date +%s%3N); redis-cli ping >/dev/null; end=$(date +%s%3N); echo $((end-start))' || echo "timeout")
        if [[ "$redis_response" == "timeout" ]] || [[ $redis_response -gt 100 ]]; then
            log_error "Redis too slow: ${redis_response}ms"
            exit 1
        fi
        
        # Current application health
        cd "$APP_ROOT/current"
        if ! php artisan about > /dev/null 2>&1; then
            log_error "Current application unhealthy"
            exit 1
        fi
        
        log_success "Production pre-deployment checks passed"
        
        # Create comprehensive backup
        log "Creating production backup..."
        backup_timestamp=$(date +"%Y%m%d_%H%M%S")
        backup_path="$BACKUP_DIR/production_$backup_timestamp"
        mkdir -p "$backup_path"
        
        # Database backup
        php artisan backup:run --only-db --filename="production_db_$backup_timestamp.sql"
        
        # Application backup
        rsync -av --exclude='node_modules' --exclude='vendor' \
              --exclude='storage/logs/*' --exclude='storage/framework/cache/*' \
              "$APP_ROOT/current"/ "$backup_path"/
        
        # Compress backup
        cd "$BACKUP_DIR"
        tar -czf "production_backup_$backup_timestamp.tar.gz" "production_$backup_timestamp"
        rm -rf "production_$backup_timestamp"
        ln -sfn "production_backup_$backup_timestamp.tar.gz" "latest_production_backup.tar.gz"
        
        # Keep only last 10 backups
        ls -t production_backup_*.tar.gz | tail -n +11 | xargs -r rm -f
        
        log_success "Production backup created"
        
        # Enable maintenance mode (unless in maintenance window)
        if [[ "$MAINTENANCE_WINDOW" != "true" ]]; then
            log "Enabling maintenance mode..."
            cd "$APP_ROOT/current"
            php artisan down \
                --message="We're performing a quick update. We'll be back shortly!" \
                --retry=60 \
                --secret="$(openssl rand -hex 16)"
            log_success "Maintenance mode enabled"
        fi
        
        # Gracefully pause queues
        log "Pausing production queues..."
        cd "$APP_ROOT/current"
        php artisan queue:pause-all
        
        # Wait for jobs to complete (max 5 minutes)
        wait_time=0
        while [[ $wait_time -lt 300 ]]; do
            active_jobs=$(pgrep -f "artisan queue:work" | wc -l)
            if [[ $active_jobs -eq 0 ]]; then
                break
            fi
            sleep 10
            wait_time=$((wait_time + 10))
            log "Waiting for $active_jobs jobs to complete..."
        done
        
        # Stop Horizon and workers
        php artisan horizon:terminate || true
        if command -v supervisorctl &> /dev/null; then
            supervisorctl stop laravel-workers-production:* 2>/dev/null || true
        fi
        
        log_success "Queues paused"
        
        # Setup deployment structure
        sudo mkdir -p "$APP_ROOT"/{releases,shared}
        sudo mkdir -p "$SHARED_DIR"/{storage,uploads}
        sudo chown -R ubuntu:www-data "$APP_ROOT"
        sudo chown -R www-data:www-data "$SHARED_DIR"
        sudo chmod -R 755 "$APP_ROOT"
        sudo chmod -R 775 "$SHARED_DIR"
        
        # Create new release
        release_timestamp=$(date +"%Y%m%d_%H%M%S")
        release_path="$APP_ROOT/releases/$release_timestamp"
        
        log "Creating production release: $release_timestamp"
        
        # Clone from main branch
        git clone --depth 1 --branch main \
            https://$<EMAIL>/SuccessionplanAI/successionplan-ai.git \
            "$release_path"
        
        cd "$release_path"
        sudo chown -R ubuntu:www-data "$release_path"
        
        # Install dependencies
        log "Installing production dependencies..."
        
        # Composer
        composer install --no-interaction --prefer-dist --no-dev --optimize-autoloader
        
        # NPM
        npm install --legacy-peer-deps --production
        npm install @inertiajs/inertia sweetalert2 react-window
        
        # Additional packages
        composer require rap2hpoutre/fast-excel phpoffice/phpword --no-interaction
        
        log_success "Dependencies installed"
        
        # Setup shared resources
        log "Setting up shared resources..."
        
        rm -rf storage
        ln -nfs "$SHARED_DIR/storage" storage
        
        if [[ -f "$SHARED_DIR/.env" ]]; then
            rm -f .env
            ln -nfs "$SHARED_DIR/.env" .env
        else
            log_error "Production .env not found"
            exit 1
        fi
        
        # Ensure shared directories
        sudo mkdir -p "$SHARED_DIR/storage"/{app,framework,logs}
        sudo mkdir -p "$SHARED_DIR/storage/framework"/{cache,sessions,views}
        sudo mkdir -p "$SHARED_DIR/storage/app"/{public,uploads}
        sudo chown -R www-data:www-data "$SHARED_DIR/storage"
        sudo chmod -R 775 "$SHARED_DIR/storage"
        
        log_success "Shared resources configured"
        
        # Build application
        log "Building production application..."
        
        if ! grep -q "APP_KEY=base64:" .env; then
            php artisan key:generate --force
        fi
        
        # Build assets
        npm run build
        
        # Cache configurations
        php artisan config:cache
        php artisan route:cache
        php artisan view:cache
        
        # Set permissions
        sudo chown -R ubuntu:www-data "$release_path"
        sudo chmod -R 755 "$release_path"
        sudo chmod -R 775 "$release_path/storage" "$release_path/bootstrap/cache"
        
        # Storage link
        if [[ ! -L "public/storage" ]]; then
            php artisan storage:link
        fi
        
        log_success "Application built"
        
        # Run migrations
        log "Running database migrations..."
        php artisan migrate --force
        log_success "Migrations completed"
        
        # Run tests (unless skipped)
        if [[ "$SKIP_TESTS" != "true" ]] && [[ -f "phpunit.xml" ]]; then
            log "Running production tests..."
            if ! timeout 600 vendor/bin/phpunit --testsuite=Feature --stop-on-failure; then
                log_error "Production tests failed"
                exit 1
            fi
            log_success "Tests passed"
        fi
        
        # Health checks
        log "Running health checks..."
        
        if ! php artisan about > /dev/null 2>&1; then
            log_error "Laravel health check failed"
            exit 1
        fi
        
        if ! php artisan tinker --execute="
            DB::connection()->getPdo();
            \$users = DB::table('users')->count();
            if (\$users < 1) throw new Exception('No users found');
        " > /dev/null 2>&1; then
            log_error "Database health check failed"
            exit 1
        fi
        
        log_success "Health checks passed"
        
        # Atomic switch to new release
        log "Switching to new release..."
        temp_link="$APP_ROOT/current_tmp_$(date +%s)"
        ln -nfs "$release_path" "$temp_link"
        mv "$temp_link" "$APP_ROOT/current"
        log_success "Release switched"
        
        # Restart production services
        log "Restarting production services..."
        
        # Test Nginx config before reload
        sudo nginx -t
        sudo systemctl reload php8.3-fpm
        sudo systemctl reload nginx
        
        # Verify services
        for service in php8.3-fpm nginx; do
            if ! systemctl is-active --quiet "$service"; then
                log_error "Service $service failed to restart"
                exit 1
            fi
        done
        
        # Clear caches
        php -r "if(function_exists('opcache_reset')) opcache_reset();"
        sync && echo 3 | sudo tee /proc/sys/vm/drop_caches > /dev/null
        
        log_success "Services restarted"
        
        # Start production queues
        log "Starting production queues..."
        cd "$APP_ROOT/current"
        
        # Start Horizon
        nohup php artisan horizon > "$SHARED_DIR/storage/logs/horizon.log" 2>&1 &
        sleep 15
        
        # Verify Horizon
        horizon_status=$(php artisan horizon:status 2>/dev/null || echo "inactive")
        if [[ "$horizon_status" != "running" ]]; then
            log_error "Horizon failed to start"
            exit 1
        fi
        
        # Start workers based on time
        current_hour=$(date +"%H")
        current_day=$(date +"%u")
        
        if command -v supervisorctl &> /dev/null; then
            if [[ $current_day -le 5 ]] && [[ $current_hour -ge 8 ]] && [[ $current_hour -lt 18 ]]; then
                supervisorctl start laravel-workers-peak:* 2>/dev/null || true
            else
                supervisorctl start laravel-workers-offpeak:* 2>/dev/null || true
            fi
        fi
        
        # Resume queues
        php artisan queue:resume-all
        
        log_success "Queues started"
        
        # Disable maintenance mode
        if [[ "$MAINTENANCE_WINDOW" != "true" ]]; then
            php artisan up
            log_success "Maintenance mode disabled"
        fi
        
        # Final verification
        sleep 30
        
        if ! php artisan about > /dev/null 2>&1; then
            log_error "Final verification failed"
            exit 1
        fi
        
        # Test critical functionality
        if ! php artisan tinker --execute="
            \$user = App\\Models\\User::first();
            if (!\$user) throw new Exception('No users found');
        " > /dev/null 2>&1; then
            log_error "Critical functionality test failed"
            exit 1
        fi
        
        # Cleanup old releases (keep last 10)
        cd "$APP_ROOT/releases"
        releases_to_remove=$(ls -t | tail -n +11)
        if [[ -n "$releases_to_remove" ]]; then
            echo "$releases_to_remove" | xargs rm -rf
            log_success "Cleaned up old releases"
        fi
        
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        
        log_critical "=========================================="
        log_critical "PRODUCTION DEPLOYMENT COMPLETED SUCCESS"
        log_critical "=========================================="
        log "Release: $release_timestamp"
        log "Duration: ${duration} seconds"
        log "Horizon: $(php artisan horizon:status)"
        log "Workers: $(supervisorctl status 2>/dev/null | grep laravel-worker | grep RUNNING | wc -l)"
        log "Completed: $(date)"
        
        # Send success notification
        php artisan queue:notification "🚀 PRODUCTION DEPLOYMENT SUCCESS: Release $release_timestamp deployed in ${duration}s by $GITHUB_ACTOR" --level="success"
        
        log_critical "PRODUCTION DEPLOYMENT COMPLETED SUCCESSFULLY"
        
        EOF
        
        echo "🎉 Production deployment completed successfully!"

    - name: Notify on Success
      if: success()
      run: |
        echo "✅ Production deployment successful"
        # Additional success notifications (Slack, email, etc.) can be added here

    - name: Notify on Failure
      if: failure()
      run: |
        echo "🚨 Production deployment FAILED"
        echo "Emergency rollback should have been initiated automatically"
        # Additional failure notifications and alerts can be added here
