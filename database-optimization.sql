-- MySQL Server Configuration for Large Packet Support
-- Run these commands as a MySQL admin to increase server-level limits

-- Increase max_allowed_packet to 256MB globally
SET GLOBAL max_allowed_packet=268435456;

-- Make the change persistent across server restarts
-- Add this to your MySQL configuration file (my.cnf or my.ini):
-- [mysqld]
-- max_allowed_packet=268435456

-- Check current setting
SHOW VARIABLES LIKE 'max_allowed_packet';

-- Optional: Also increase other related limits if needed
-- SET GLOBAL innodb_buffer_pool_size=1073741824; -- 1GB
-- SET GLOBAL innodb_log_file_size=268435456; -- 256MB