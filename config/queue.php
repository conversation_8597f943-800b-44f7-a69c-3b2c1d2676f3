<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Queue Connection Name
    |--------------------------------------------------------------------------
    |
    | Laravel's queue API supports an assortment of back-ends via a single
    | API, giving you convenient access to each back-end using the same
    | syntax for every one. Here you may define a default connection.
    |
    */

    'default' => env('QUEUE_CONNECTION', 'sync'),

    /*
    |--------------------------------------------------------------------------
    | Queue Connections
    |--------------------------------------------------------------------------
    |
    | Here you may configure the connection information for each server that
    | is used by your application. A default configuration has been added
    | for each back-end shipped with Laravel. You are free to add more.
    |
    | Drivers: "sync", "database", "beanstalkd", "sqs", "redis", "null"
    |
    */

    'connections' => [

        'sync' => [
            'driver' => 'sync',
        ],

        'database' => [
            'driver' => 'database',
            'table' => 'job_queues',
            'queue' => 'default',
            'retry_after' => 1800, // Increased from 300 to 1800 seconds (30 minutes)
            'after_commit' => false,
        ],

        'beanstalkd' => [
            'driver' => 'beanstalkd',
            'host' => 'localhost',
            'queue' => 'default',
            'retry_after' => 90,
            'block_for' => 0,
            'after_commit' => false,
        ],

        'sqs' => [
            'driver' => 'sqs',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'prefix' => env('SQS_PREFIX', 'https://sqs.us-east-1.amazonaws.com/your-account-id'),
            'queue' => env('SQS_QUEUE', 'default'),
            'suffix' => env('SQS_SUFFIX'),
            'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
            'after_commit' => false,
        ],

        'redis' => [
            'driver' => 'redis',
            'connection' => 'default',
            'queue' => env('REDIS_QUEUE', 'default'),
            'retry_after' => 1800, // Match our job timeout
            'block_for' => env('REDIS_QUEUE_BLOCK_FOR', 5), // Block for 5 seconds before polling
            'after_commit' => false,
        ],

        // High-priority queue for internal searches (fast database queries)
        'redis_internal' => [
            'driver' => 'redis',
            'connection' => 'default',
            'queue' => 'internal_search',
            'retry_after' => 900, // 15 minutes for faster internal operations
            'block_for' => env('REDIS_INTERNAL_BLOCK_FOR', 2), // Faster polling for high-priority jobs
            'after_commit' => false,
        ],

        // Lower-priority queue for external searches (API calls, slower operations)
        'redis_external' => [
            'driver' => 'redis',
            'connection' => 'default',
            'queue' => 'external_search',
            'retry_after' => 2400, // 40 minutes for slower external operations
            'block_for' => env('REDIS_EXTERNAL_BLOCK_FOR', 10), // Slower polling for low-priority jobs
            'after_commit' => false,
        ],

        // Dedicated queue for succession plan creation
        'redis_succession' => [
            'driver' => 'redis',
            'connection' => 'default',
            'queue' => 'succession-plans',
            'retry_after' => 600, // 10 minutes for succession plan operations
            'block_for' => env('REDIS_SUCCESSION_BLOCK_FOR', 3),
            'after_commit' => false,
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Job Batching
    |--------------------------------------------------------------------------
    |
    | The following options configure the database and table that store job
    | batching information. These options can be updated to any database
    | connection and table which has been defined by your application.
    |
    */

    'batching' => [
        'database' => env('DB_CONNECTION', 'mysql'),
        'table' => 'job_batches',
    ],

    /*
    |--------------------------------------------------------------------------
    | Failed Queue Jobs
    |--------------------------------------------------------------------------
    |
    | These options configure the behavior of failed queue job logging so you
    | can control which database and table are used to store the jobs that
    | have failed. You may change them to any database / table you wish.
    |
    */

    'failed' => [
        'driver' => env('QUEUE_FAILED_DRIVER', 'database-uuids'),
        'database' => env('DB_CONNECTION', 'mysql'),
        'table' => 'failed_jobs',
    ],

];
