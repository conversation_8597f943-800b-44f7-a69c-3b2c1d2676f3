<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\SearchCandidatesJob;
use App\Models\SuccessionPlan;
use App\Models\User;
use App\Models\SuccessRequirements;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class RerunPlan extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'plan:rerun {plan_id : The ID of the succession plan to rerun}
                                      {--type=both : Type of search to run (both, internal, external)}
                                      {--info : Show plan information without running}
                                      {--user= : User ID to run the job as (defaults to plan creator)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rerun internal and/or external searches for an existing plan';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $planId = $this->argument('plan_id');
        $searchType = $this->option('type');
        $showInfo = $this->option('info');
        
        // Load the succession plan
        $plan = SuccessionPlan::find($planId);
        
        if (!$plan) {
            $this->error("Plan not found with ID: {$planId}");
            return 1;
        }
        
        // Check if plan has the new fields or use plan_data JSON
        if (!empty($plan->plan_data)) {
            // Use the stored plan_data JSON
            $planData = is_string($plan->plan_data) ? json_decode($plan->plan_data, true) : $plan->plan_data;
            $planData['plan_id'] = $plan->id; // Ensure plan_id is always set
            $planData['bypass_duplicate_check'] = true;
            $planData['is_rerun'] = true;
            $planData['user_id'] = $plan->user_id;
        } else if (!empty($plan->target_roles)) {
            // Use individual fields if available
            $planData = [
                'plan_id' => $plan->id,
                'plan_name' => $plan->plan_name ?? $plan->name,
                'description' => $plan->description,
                'target_roles' => $plan->target_roles ?? [],
                'alternative_roles_titles' => $plan->alternative_roles_titles ?? [],
                'acronyms' => $plan->acronyms ?? [],
                'companies' => $plan->companies ?? [],
                'minimum_tenure' => (int)($plan->minimum_tenure ?? 0),
                'step_up_candidates' => $plan->step_up_candidates ?? ['none'],
                'gender' => $plan->gender ?? 'Not required',
                'country' => $plan->country ?? [],
                'include_alumni' => (bool)($plan->include_alumni ?? false),
                'is_ethnicity_important' => (bool)($plan->ethnicity ?? false),
                'qualifications' => $plan->qualifications ?? [],
                'skills' => $plan->skills ?? [],
                'available_companies' => [],
                'user_id' => $plan->user_id,
                'bypass_duplicate_check' => true,
                'is_rerun' => true
            ];
        } else {
            // Fallback: Reconstruct from requirements table (for old plans)
            $requirements = SuccessRequirements::where('plan_id', $planId)->get();
            
            $planData = [
                'plan_id' => $plan->id,
                'plan_name' => $plan->name,
                'description' => $plan->description,
                'target_roles' => $requirements->where('type', 'Role')->pluck('name')->toArray(),
                'alternative_roles_titles' => [],
                'acronyms' => [],
                'companies' => $requirements->where('type', 'Company')->pluck('name')->toArray(),
                'minimum_tenure' => (int)($plan->minimum_Experience ?? 0),
                'step_up_candidates' => $requirements->where('type', 'step_up')->pluck('name')->toArray() ?: ['none'],
                'gender' => $requirements->where('type', 'Gender')->first()->name ?? 'Not required',
                'country' => $requirements->where('type', 'location')->pluck('name')->toArray(),
                'include_alumni' => (bool)($requirements->where('type', 'include_alumni')->first()->name ?? false),
                'is_ethnicity_important' => (bool)($plan->ethnicity ?? false),
                'qualifications' => $requirements->where('type', 'education')->pluck('name')->toArray(),
                'skills' => $requirements->where('type', 'professional_skill')->pluck('name')->toArray(),
                'available_companies' => [],
                'user_id' => $plan->user_id,
                'bypass_duplicate_check' => true,
                'is_rerun' => true
            ];
        }
        
        // Log the reconstructed plan data
        Log::info("Reconstructed plan data for rerun", [
            'plan_id' => $planId,
            'plan_name' => $plan->plan_name ?? $plan->name,
            'data' => $planData
        ]);
        
        // Display plan info
        $this->info("Rerunning searches for plan: " . ($plan->plan_name ?? $plan->name));
        $this->info("Plan ID: {$planId}");
        $this->info("Search type: {$searchType}");
        
        if ($showInfo) {
            $this->table(
                ['Field', 'Value'],
                [
                    ['Plan Name', $plan->plan_name ?? $plan->name],
                    ['Target Roles', implode(', ', $planData['target_roles'])],
                    ['Companies', implode(', ', $planData['companies'])],
                    ['Countries', implode(', ', $planData['country'])],
                    ['Include Alumni', $planData['include_alumni'] ? 'Yes' : 'No'],
                    ['Min Tenure', $planData['minimum_tenure'] . ' years'],
                    ['Gender', $planData['gender']],
                    ['Skills', implode(', ', array_slice($planData['skills'], 0, 3)) . (count($planData['skills']) > 3 ? '...' : '')],
                ]
            );
            return 0;
        }
        
        // Get user
        $userId = $this->option('user') ?? $plan->user_id;
        $user = User::find($userId);
        
        if (!$user) {
            $this->error("User not found with ID: {$userId}");
            return 1;
        }
        
        // Determine search type
        $searchTypeMap = [
            'both' => SearchCandidatesJob::SEARCH_BOTH,
            'internal' => SearchCandidatesJob::SEARCH_INTERNAL,
            'external' => SearchCandidatesJob::SEARCH_EXTERNAL
        ];
        
        $jobSearchType = $searchTypeMap[$searchType] ?? SearchCandidatesJob::SEARCH_BOTH;
        
        // Clear duplicate detection key
        $duplicateKey = "search_job:{$planId}:{$searchType}";
        Redis::del($duplicateKey);
        
        // Clear with Laravel prefix as well
        $prefixedKey = "Laravel_database_search_job:{$planId}:{$searchType}";
        Redis::del($prefixedKey);
        
        // Dispatch the job
        SearchCandidatesJob::dispatch($planData, $user, $jobSearchType);
        
        $this->info("✓ " . ucfirst($searchType) . " search job dispatched");
        $this->newLine();
        $this->info("Jobs queued successfully! Check your queue worker logs for progress.");
        
        Log::info("Plan rerun initiated", [
            'plan_id' => $planId,
            'plan_name' => $plan->plan_name ?? $plan->name,
            'search_type' => $searchType,
            'user_id' => $userId,
            'initiated_by' => 'console_command'
        ]);
        
        return 0;
    }
}