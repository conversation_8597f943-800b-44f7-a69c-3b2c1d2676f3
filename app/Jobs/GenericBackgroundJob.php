<?php

namespace App\Jobs;

use App\Jobs\Traits\ExponentialBackoffRetry;
use App\Models\BackgroundProcess;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Throwable;

class GenericBackgroundJob implements ShouldQueue
{
    use Dispatchable, ExponentialBackoffRetry, InteractsWithQueue, Queueable, SerializesModels;

    // Timeout for generic background operations
    public int $timeout = 1800; // 30 minutes for general operations

    // Retry configuration
    public int $tries = 3;

    protected string $class;

    protected string $method;

    protected array $parameters;

    protected int $processId;

    /**
     * Create a new job instance.
     *
     * @param  string  $class  The class name to call
     * @param  string  $method  The method name to call
     * @param  array  $parameters  Parameters to pass to the method
     * @param  int  $processId  The background process ID for tracking
     */
    public function __construct(string $class, string $method, array $parameters, int $processId)
    {
        $this->class = $class;
        $this->method = $method;
        $this->parameters = $parameters;
        $this->processId = $processId;

        // Configure exponential backoff for generic operations
        $this->setBaseDelay(30); // 30 seconds base delay
        $this->setMaxDelay(900); // 15 minutes max delay
        $this->setMaxRetryTime(120); // 2 hours total retry window
    }

    /**
     * Execute the job.
     * Following course guidelines for defensive programming
     */
    public function handle(): void
    {
        // Validate process exists (defensive programming from course Lesson 8)
        $process = BackgroundProcess::find($this->processId);
        if (! $process) {
            Log::warning("GENERIC JOB: Process {$this->processId} not found, skipping job", [
                'class' => $this->class,
                'method' => $this->method,
            ]);

            return;
        }

        // Idempotent check - skip if already completed
        if ($process->status === 'completed') {
            Log::info("GENERIC JOB: Process {$this->processId} already completed, skipping", [
                'class' => $this->class,
                'method' => $this->method,
            ]);

            return;
        }

        Log::info('GENERIC JOB: Starting background process', [
            'process_id' => $this->processId,
            'class' => $this->class,
            'method' => $this->method,
        ]);

        // Mark as pending (since 'running' is not an allowed status in the enum)
        // We'll consider it 'pending' until it's either completed or failed
        $process->update(['status' => 'pending']);
        $process->refresh();

        try {
            // Validate class and method exist before execution
            if (!class_exists($this->class)) {
                throw new \InvalidArgumentException("Class does not exist: {$this->class}");
            }

            if (!method_exists($this->class, $this->method)) {
                throw new \InvalidArgumentException("Method does not exist: {$this->class}::{$this->method}");
            }

            // Execute the method dynamically
            $result = call_user_func_array([$this->class, $this->method], $this->parameters);

            // Store successful result
            $process->update([
                'status' => 'completed',
                'output' => json_encode($result),
                // Note: completed_at column doesn't exist in the database
            ]);

            Log::info('GENERIC JOB: Process completed successfully', [
                'process_id' => $this->processId,
                'class' => $this->class,
                'method' => $this->method,
                'result_size' => strlen(json_encode($result)),
            ]);

        } catch (\Exception $e) {
            // Update process status to failed
            $process->update([
                'status' => 'failed',
                'output' => $e->getMessage(),
                'failed_at' => now(),
            ]);

            // Re-throw to trigger job failure handling
            throw $e;
        }
    }

    /**
     * Handle job failure (course Lesson 6 requirement)
     */
    public function failed(?Throwable $exception): void
    {
        // Enhanced failure logging following course guidelines
        $logContext = [
            'job' => static::class,
            'process_id' => $this->processId,
            'class' => $this->class,
            'method' => $this->method,
            'parameters' => $this->parameters,
            'exception' => $exception?->getMessage(),
            'max_attempts' => $this->tries,
            'backoff_delays' => $this->backoff(),
            'retry_until' => $this->retryUntil()->toDateTimeString(),
            'trace' => $exception?->getTraceAsString(),
        ];

        Log::error('GENERIC JOB: Background process failed after all retry attempts', $logContext);

        // Update the process record to reflect final failure
        try {
            $process = BackgroundProcess::find($this->processId);
            
            if ($process) {
                $errorMessage = 'Job failed after '.$this->tries.' attempts: '.($exception?->getMessage() ?? 'Unknown error');
                
                $process->update([
                    'status' => 'failed',
                    'output' => $errorMessage,
                    'failed_at' => now(),
                ]);
                
                // Refresh to ensure we have the latest data
                $process->refresh();
            } else {
                Log::warning('GENERIC JOB: Process not found during failure handling', [
                    'process_id' => $this->processId,
                    'error' => 'Process not found in database'
                ]);
            }
        } catch (\Exception $e) {
            // Log the error but don't throw to prevent masking the original exception
            Log::warning('GENERIC JOB: Failed to update process record after job failure', [
                'process_id' => $this->processId,
                'error' => $e->getMessage(),
                'original_exception' => $exception?->getMessage(),
            ]);
            Log::warning('GENERIC JOB: Failed to update process record after job failure', [
                'process_id' => $this->processId,
                'error' => $e->getMessage(),
            ]);
        }

    }

    /**
     * Set retry until timestamp (course recommendation)
     */
    public function retryUntil(): Carbon
    {
        return now()->addHours(2); // 2 hour retry window for generic operations
    }
}
