<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\AI\ExternalPeopleSearch;
use App\Jobs\Traits\ExponentialBackoffRetry;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Throwable;

class TalentPoolExternalSearch implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, ExponentialBackoffRetry;

    // Timeout for external operations following course standards
    public int $timeout = 2400; // 40 minutes for external API operations
    
    // Retry configuration for external operations
    public int $tries = 5;

    protected array $planData;
    protected int $userId;

    /**
     * Create a new job instance.
     *
     * @param array $planData The succession plan data
     * @param int $userId The user ID (following course idempotency guidelines)
     */
    public function __construct(array $planData, int $userId)
    {
        $this->planData = $planData;
        $this->userId = $userId;
        
        // Set queue for external operations
        $this->onQueue('external_search');
        
        // Configure exponential backoff for external operations (course best practice)
        $this->setBaseDelay(60); // 1 minute base delay
        $this->setMaxDelay(1800); // 30 minutes max delay
        $this->setMaxRetryTime(240); // 4 hours total retry window
        $this->enableJitter(true); // Prevent thundering herd with external APIs
    }

    /**
     * Execute the job.
     * Following course guidelines for idempotent job execution
     */
    public function handle(ExternalPeopleSearch $externalSearchService): void
    {
        // Get user (defensive programming from course Lesson 8)
        $user = User::find($this->userId);
        if (!$user) {
            Log::warning("EXTERNAL SEARCH: User {$this->userId} not found, skipping job", [
                'plan_id' => $this->planData['plan_id'] ?? 'unknown'
            ]);
            return;
        }

        // Idempotent check (course Lesson 8 requirement)
        if ($this->isAlreadyProcessed()) {
            Log::info("EXTERNAL SEARCH: Search already processed for plan {$this->planData['plan_id']}, skipping", [
                'plan_id' => $this->planData['plan_id'],
                'user_id' => $this->userId
            ]);
            return;
        }

        Log::info("EXTERNAL SEARCH: Starting external talent search", [
            'plan_id' => $this->planData['plan_id'] ?? 'unknown',
            'plan_name' => $this->planData['plan_name'] ?? 'Unnamed Plan',
            'user_id' => $this->userId
        ]);

        // Perform external search using proper service injection
        $searchResults = $externalSearchService->searchExternalCandidates($this->planData, $user);
        
        Log::info("EXTERNAL SEARCH: Search completed", [
            'plan_id' => $this->planData['plan_id'] ?? 'unknown',
            'candidates_found' => count($searchResults),
            'user_id' => $this->userId
        ]);

        // Create completion notification only if search was successful
        if (!empty($searchResults)) {
            $this->createSearchCompletionNotification();
        }
    }

    /**
     * Handle job failure (course Lesson 6 requirement)
     * 
     * @param Throwable|null $exception
     * @return void
     */
    public function failed(?Throwable $exception): void
    {
        // Enhanced failure logging following course guidelines
        Log::error('EXTERNAL SEARCH: Job failed after all retry attempts', [
            'job' => static::class,
            'plan_id' => $this->planData['plan_id'] ?? 'unknown',
            'plan_name' => $this->planData['plan_name'] ?? 'Unnamed Plan',
            'user_id' => $this->userId,
            'exception' => $exception?->getMessage(),
            'max_attempts' => $this->tries,
            'backoff_delays' => $this->backoff(),
            'retry_until' => $this->retryUntil()->toDateTimeString(),
            'trace' => $exception?->getTraceAsString()
        ]);

        // Cleanup any partial data that might have been created
        $this->cleanupPartialData();

        // Create error notification for user
        $this->createErrorNotification($exception);


    }

    /**
     * Check if this search has already been processed (idempotent check)
     * Following course Lesson 8 guidelines
     * 
     * @return bool
     */
    protected function isAlreadyProcessed(): bool
    {
        $planId = $this->planData['plan_id'] ?? null;
        if (!$planId) {
            return false;
        }

        // Check if we already have a completion notification for this plan and user
        return DB::table('job_queues_notification')
            ->where('user_id', $this->userId)
            ->where('plan_id', $planId)
            ->where('job_type', 'external_search')
            ->where('status', 'completed')
            ->exists();
    }

    /**
     * Create notification for successful search completion
     */
    protected function createSearchCompletionNotification(): void
    {
        $notificationData = [
            'user_id' => $this->userId,
            'plan_id' => $this->planData['plan_id'] ?? null,
            'job_type' => 'external_search',
            'status' => 'completed',
            'message' => 'External talent search completed successfully',
            'created_at' => now(),
            'updated_at' => now(),
        ];

        DB::table('job_queues_notification')->insert($notificationData);

        Log::info("EXTERNAL SEARCH: Completion notification created", [
            'plan_id' => $this->planData['plan_id'] ?? 'unknown',
            'user_id' => $this->userId
        ]);
    }

    /**
     * Create notification for search error
     * 
     * @param Throwable|null $exception
     */
    protected function createErrorNotification(?Throwable $exception): void
    {
        $notificationData = [
            'user_id' => $this->userId,
            'plan_id' => $this->planData['plan_id'] ?? null,
            'job_type' => 'external_search',
            'status' => 'failed',
            'message' => 'External talent search failed: ' . ($exception?->getMessage() ?? 'Unknown error'),
            'created_at' => now(),
            'updated_at' => now(),
        ];

        DB::table('job_queues_notification')->insert($notificationData);

        Log::info("EXTERNAL SEARCH: Error notification created", [
            'plan_id' => $this->planData['plan_id'] ?? 'unknown',
            'user_id' => $this->userId,
            'error' => $exception?->getMessage()
        ]);
    }

    /**
     * Clean up any partial data created before failure
     * Following course best practices for failure recovery
     */
    protected function cleanupPartialData(): void
    {
        try {
            $planId = $this->planData['plan_id'] ?? null;
            if ($planId) {
                // Remove any incomplete search results for this plan
                DB::table('pipeline')
                    ->where('plan_id', $planId)
                    ->where('type', 'External-System')
                    ->where('created_at', '>', now()->subHour()) // Only recent incomplete data
                    ->whereNull('completed_at') // Assume completed_at marks full processing
                    ->delete();

                Log::info("EXTERNAL SEARCH: Cleaned up partial data for plan {$planId}");
            }
        } catch (\Exception $e) {
            Log::warning("EXTERNAL SEARCH: Failed to cleanup partial data", [
                'plan_id' => $planId ?? 'unknown',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Set retry until timestamp (course recommendation)
     * 
     * @return Carbon
     */
    public function retryUntil(): Carbon
    {
        return now()->addHours(4); // 4 hour retry window for external operations
    }
}
