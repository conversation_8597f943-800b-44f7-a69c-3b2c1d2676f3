<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SuccessionPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'plan_name',
        'description',
        'minimum_Experience',
        'minimum_tenure',
        'step_up',
        'ethnicity',
        'tagged_individual',
        'status',
        'shared_with',
        'user_id',
        'age',
        'last_opened',
        'candidate_status',
        'mover',
        // New fields
        'target_roles',
        'companies',
        'country',
        'gender',
        'include_alumni',
        'skills',
        'qualifications',
        'step_up_candidates',
        'alternative_roles_titles',
        'acronyms',
        'plan_data',
        'job_description'
    ];
    
    protected $casts = [
        'target_roles' => 'array',
        'companies' => 'array',
        'country' => 'array',
        'skills' => 'array',
        'qualifications' => 'array',
        'step_up_candidates' => 'array',
        'alternative_roles_titles' => 'array',
        'acronyms' => 'array',
        'plan_data' => 'array',
        'include_alumni' => 'boolean'
    ];

    public function successPeople()
    {
        return $this->hasMany(SuccessPeople::class, 'plan_id');
    }

    public function user()
    {   
        return $this->belongsTo(user::class);
    }

    public function requirements()
    {
        return $this->hasMany(SuccessRequirements::class, 'plan_id');
    }
}
