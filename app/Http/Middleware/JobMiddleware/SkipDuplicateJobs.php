<?php

namespace App\Http\Middleware\JobMiddleware;

use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;

class SkipDuplicateJobs
{
    /**
     * Process the queued job.
     */
    public function handle($job, $next)
    {
        // Check if duplicate detection should be bypassed (for reruns)
        if ($this->shouldBypassDuplicateCheck($job)) {
            Log::info('Bypassing duplicate check for rerun job', [
                'job' => get_class($job),
                'job_id' => $this->getJobId($job)
            ]);
            return $next($job);
        }
        
        $key = $this->getDuplicateKey($job);
        $ttl = $this->getTtl($job);

        // Check if this job is already processing or recently completed
        if (Redis::exists($key)) {
            Log::info('Skipping duplicate job', [
                'job' => get_class($job),
                'key' => $key,
                'job_id' => $this->getJobId($job)
            ]);

            // Skip this job by returning false
            return false;
        }

        // Mark this job as processing
        Redis::setex($key, $ttl, json_encode([
            'job_id' => $this->getJobId($job),
            'started_at' => now()->toDateTimeString(),
            'class' => get_class($job)
        ]));

        try {
            $result = $next($job);
            
            // Job completed successfully, extend the TTL to prevent duplicates
            Redis::expire($key, $this->getCompletionTtl($job));
            
            return $result;
        } catch (\Exception $e) {
            // Job failed, remove the lock to allow retries
            Redis::del($key);
            throw $e;
        }
    }

    /**
     * Get the duplicate detection key for the job
     */
    private function getDuplicateKey($job): string
    {
        // If job has custom duplicate key method, use it
        if (method_exists($job, 'getDuplicateKey')) {
            return $job->getDuplicateKey();
        }

        // Generate key based on job class and serialized properties
        $className = get_class($job);
        $payload = $this->getJobPayload($job);
        
        return "duplicate_job:{$className}:" . md5($payload);
    }

    /**
     * Get job payload for duplicate detection
     */
    private function getJobPayload($job): string
    {
        // If job has custom payload method for duplicate detection
        if (method_exists($job, 'getDuplicatePayload')) {
            return serialize($job->getDuplicatePayload());
        }

        // Use reflection to get public properties
        $reflection = new \ReflectionClass($job);
        $properties = [];
        
        foreach ($reflection->getProperties(\ReflectionProperty::IS_PUBLIC) as $property) {
            $properties[$property->getName()] = $property->getValue($job);
        }

        return serialize($properties);
    }

    /**
     * Get TTL for duplicate detection while processing
     */
    private function getTtl($job): int
    {
        if (method_exists($job, 'getDuplicateTtl')) {
            return $job->getDuplicateTtl();
        }

        // Default TTL based on job type (in seconds)
        $defaults = [
            'App\Jobs\SearchCandidatesJob' => 300, // 5 minutes
            'App\Jobs\ExternalSearchPerplexityJob' => 600, // 10 minutes
            'App\Jobs\CreateSuccessionPlan' => 1800, // 30 minutes
        ];

        return $defaults[get_class($job)] ?? 300;
    }

    /**
     * Get TTL for duplicate detection after completion
     */
    private function getCompletionTtl($job): int
    {
        if (method_exists($job, 'getCompletionTtl')) {
            return $job->getCompletionTtl();
        }

        // Prevent duplicates for longer after successful completion
        return $this->getTtl($job) * 4; // 4x the processing TTL
    }

    /**
     * Get job ID from the job instance
     */
    private function getJobId($job): string
    {
        // Try to get the job ID from the underlying queue job instance
        if (property_exists($job, 'job') && $job->job && method_exists($job->job, 'getJobId')) {
            return $job->job->getJobId();
        }

        // If job has a custom method to provide ID
        if (method_exists($job, 'getJobId')) {
            return $job->getJobId();
        }

        // Fall back to generating a unique ID based on job properties
        return uniqid(get_class($job) . '_', true);
    }
    
    /**
     * Check if duplicate detection should be bypassed
     */
    private function shouldBypassDuplicateCheck($job): bool
    {
        // Use reflection to access protected property
        try {
            $reflection = new \ReflectionClass($job);
            
            if ($reflection->hasProperty('planData')) {
                $property = $reflection->getProperty('planData');
                $property->setAccessible(true);
                $planData = $property->getValue($job);
                
                if (is_array($planData)) {
                    return !empty($planData['bypass_duplicate_check']) || !empty($planData['is_rerun']);
                }
            }
        } catch (\Exception $e) {
            // If reflection fails, continue with other checks
        }
        
        // Check if job has a method to determine bypass
        if (method_exists($job, 'shouldBypassDuplicateCheck')) {
            return $job->shouldBypassDuplicateCheck();
        }
        
        return false;
    }
}
