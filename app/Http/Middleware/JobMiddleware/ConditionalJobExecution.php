<?php

namespace App\Http\Middleware\JobMiddleware;

use Illuminate\Queue\Middleware\Skip;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ConditionalJobExecution
{
    /**
     * Process the queued job.
     */
    public function handle($job, $next)
    {
        // Skip if outside business hours for non-critical jobs
        if ($this->shouldSkipOutsideBusinessHours($job)) {
            Log::info('Skipping job outside business hours', [
                'job' => get_class($job),
                'current_time' => Carbon::now()->toDateTimeString()
            ]);

            // Reschedule for next business day
            $job->release($this->getBusinessHoursDelay());
            return;
        }

        // Skip if system is under maintenance
        if ($this->isSystemUnderMaintenance($job)) {
            Log::info('Skipping job during maintenance', [
                'job' => get_class($job)
            ]);

            $job->release(300); // Retry in 5 minutes
            return;
        }

        // Skip if resource constraints are too high
        if ($this->shouldSkipDueToResourceConstraints($job)) {
            Log::info('Skipping job due to resource constraints', [
                'job' => get_class($job),
                'memory_usage' => memory_get_usage(true),
                'cpu_load' => sys_getloadavg()[0] ?? 0
            ]);

            $job->release(120); // Retry in 2 minutes
            return;
        }

        // Skip if dependent services are unavailable
        if ($this->shouldSkipDueToServiceUnavailability($job)) {
            Log::warning('Skipping job due to service unavailability', [
                'job' => get_class($job)
            ]);

            $job->release(180); // Retry in 3 minutes
            return;
        }

        // Check job-specific conditions
        if (method_exists($job, 'shouldSkip') && $job->shouldSkip()) {
            Log::info('Job requested to be skipped', [
                'job' => get_class($job)
            ]);

            return Skip::job();
        }

        $next($job);
    }

    /**
     * Check if job should be skipped outside business hours
     */
    private function shouldSkipOutsideBusinessHours($job): bool
    {
        // Critical jobs run 24/7
        $criticalJobs = [
            'App\Jobs\CreateSuccessionPlan',
            'App\Jobs\SearchCandidatesJob',
            'App\Jobs\SearchInternalPeopleJob', 
            'App\Jobs\TalentPoolExternalSearch',
            'App\Jobs\ExternalSearchPerplexityJob',
            'App\Jobs\ProcessAnthropicRequest',
            'App\Jobs\ProcessExaRequest',
            'App\Jobs\ProcessOpenAiRequest',
            'App\Jobs\GenericBackgroundJob',
            'App\Jobs\SendNewAdminEmail',
            'App\Jobs\CleanupOldLogFiles',
        ];

        if (in_array(get_class($job), $criticalJobs)) {
            return false;
        }

        $now = Carbon::now();
        $businessStart = Carbon::today()->setTime(8, 0); // 8 AM
        $businessEnd = Carbon::today()->setTime(18, 0);   // 6 PM

        // Skip on weekends for non-critical jobs
        if ($now->isWeekend()) {
            return true;
        }

        // Skip outside business hours
        return $now->lt($businessStart) || $now->gt($businessEnd);
    }

    /**
     * Check if system is under maintenance
     */
    private function isSystemUnderMaintenance($job): bool
    {
        // Check maintenance mode file
        if (file_exists(storage_path('framework/maintenance.php'))) {
            return true;
        }

        // Check maintenance flag in cache
        return cache()->has('system_maintenance');
    }

    /**
     * Check if should skip due to resource constraints
     */
    private function shouldSkipDueToResourceConstraints($job): bool
    {
        // Temporarily disabled for testing
        return false;
        
        // Memory threshold (80% of available memory)
        $memoryLimit = $this->convertToBytes(ini_get('memory_limit'));
        $memoryUsage = memory_get_usage(true);
        
        if ($memoryUsage > ($memoryLimit * 0.8)) {
            return true;
        }

        // CPU load threshold (10.0 for local development - temporarily increased)
        $loadAverage = sys_getloadavg();
        if (isset($loadAverage[0]) && $loadAverage[0] > 10.0) {
            return true;
        }

        return false;
    }

    /**
     * Check if should skip due to service unavailability
     */
    private function shouldSkipDueToServiceUnavailability($job): bool
    {
        $jobClass = get_class($job);

        // Check database connection for jobs that need it
        if (strpos($jobClass, 'Search') !== false || strpos($jobClass, 'Create') !== false) {
            try {
                \DB::connection()->getPdo();
            } catch (\Exception $e) {
                return true;
            }
        }

        // Check Redis connection for jobs that use caching
        if (strpos($jobClass, 'External') !== false) {
            try {
                \Redis::ping();
            } catch (\Exception $e) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get delay until next business hours
     */
    private function getBusinessHoursDelay(): int
    {
        $now = Carbon::now();
        $nextBusinessDay = $now->copy();

        // If it's weekend, move to Monday
        if ($now->isWeekend()) {
            $nextBusinessDay = $now->next(Carbon::MONDAY);
        }

        // Set to 8 AM
        $nextBusinessDay->setTime(8, 0, 0);

        // If we're past business hours today, move to next day
        if ($now->format('H') >= 18) {
            $nextBusinessDay->addDay();
            
            // If next day is weekend, move to Monday
            if ($nextBusinessDay->isWeekend()) {
                $nextBusinessDay = $nextBusinessDay->next(Carbon::MONDAY);
            }
        }

        return $nextBusinessDay->diffInSeconds($now);
    }

    /**
     * Convert memory string to bytes
     */
    private function convertToBytes(string $value): int
    {
        $unit = strtolower(substr($value, -1, 1));
        $value = (int) $value;

        switch ($unit) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }
}
