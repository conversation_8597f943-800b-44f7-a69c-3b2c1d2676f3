<?php

namespace App\Http\Middleware\JobMiddleware;

use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class RateLimitJobs
{
    /**
     * Process the queued job.
     */
    public function handle($job, $next)
    {
        $key = $this->getRateLimitKey($job);
        $maxAttempts = $this->getMaxAttempts($job);
        $decayMinutes = $this->getDecayMinutes($job);

        $attempts = Redis::incr($key);
        
        if ($attempts === 1) {
            Redis::expire($key, $decayMinutes * 60);
        }

        if ($attempts > $maxAttempts) {
            Log::warning('Job rate limited', [
                'job' => get_class($job),
                'key' => $key,
                'attempts' => $attempts,
                'max_attempts' => $maxAttempts
            ]);

            // Release the job back to queue with delay
            $job->release($this->getRetryDelay($job));
            return;
        }

        $next($job);
    }

    /**
     * Get the rate limit key for the job
     */
    private function getRateLimitKey($job): string
    {
        $className = get_class($job);
        
        // If job has custom rate limit key method, use it
        if (method_exists($job, 'getRateLimitKey')) {
            return $job->getRateLimitKey();
        }

        // Default key based on class name and current minute
        return "rate_limit:{$className}:" . Carbon::now()->format('Y-m-d-H-i');
    }

    /**
     * Get max attempts for the job
     */
    private function getMaxAttempts($job): int
    {
        if (method_exists($job, 'getMaxAttempts')) {
            return $job->getMaxAttempts();
        }

        // Default rate limits by job type
        $defaults = [
            'App\Jobs\SearchCandidatesJob' => 10,
            'App\Jobs\ExternalSearchPerplexityJob' => 5,
            'App\Jobs\CreateSuccessionPlan' => 20,
        ];

        return $defaults[get_class($job)] ?? 10;
    }

    /**
     * Get decay minutes for rate limit
     */
    private function getDecayMinutes($job): int
    {
        if (method_exists($job, 'getRateLimitDecay')) {
            return $job->getRateLimitDecay();
        }

        return 1; // Default 1 minute window
    }

    /**
     * Get retry delay when rate limited
     */
    private function getRetryDelay($job): int
    {
        if (method_exists($job, 'getRateLimitRetryDelay')) {
            return $job->getRateLimitRetryDelay();
        }

        return 60; // Default 60 seconds
    }
}
