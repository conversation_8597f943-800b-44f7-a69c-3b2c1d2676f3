<?php
namespace App\Http\Controllers;

use App\Models\CareerHistories;
use App\Models\notifications;
use App\Models\People;
use App\Models\InternalPeople;
use App\Models\pipeline;
use App\Models\Skills;
use App\Models\Account;
use App\Models\Company;
use App\Models\SuccessionPlan;
use App\Models\SuccessRequirements;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Jobs\SearchCandidatesJob;
use Illuminate\Support\Facades\Response;
use DateTime;
use App\Services\AI\PlanDataProcessor;
use App\Services\AI\ResearchFunctionality;

class AiChatbot extends Controller
{
    // ======================================================
    // PROPERTIES AND CONFIGURATION
    // ======================================================
    
    /**
     * API key for Claude API
     */
    private $anthropicApiKey;
    
    /**
     * Model name for Claude API
     */
    private $modelName;
    
    /**
     * API key for other external services
     */
    public $apiKey;
    
    /**
     * Plan data processor service
     */
    protected $planProcessor;
    
    /**
     * Research functionality service
     */
    protected $researchService;

    /**
     * Constructor
     */
    public function __construct(PlanDataProcessor $planProcessor, ResearchFunctionality $researchService)
    {
        $this->anthropicApiKey = config('ai.anthropic.api_key');
        $this->modelName = config('ai.anthropic.model');
        $this->apiKey = config('ai.nubela.api_key');
        $this->planProcessor = $planProcessor;
        $this->researchService = $researchService;
    }

    // ======================================================
    // ENTRY POINT METHODS
    // ======================================================

    /**
     * Initial entry point - clears session and renders the chat interface
     */
    public function index(Request $request)
    {
        // Clear the conversation from the session to reset the chat on page load
        $request->session()->forget('messages');

        // Also clear the creating_plan flag to ensure a fresh start
        $request->session()->forget('creating_plan');

        return view('aichatbot.index');
    }

    /**
     * Main entry point for message handling - determines intent and routes accordingly
     * Acts as an orchestrator by classifying intents into "Create Plan" or "Research"
     */
    public function sendMessage(Request $request)
    {
        $user = auth()->user();
        $userMessage = $request->input('message');
        $planData = [];

        // Retrieve previous messages from the session
        $allMessages = $request->session()->get('messages', []);

        // Add the user's message to the full conversation
        $allMessages[] = [
            'role' => 'user',
            'content' => $userMessage
        ];

        // Check if we're already in a plan creation flow
        $isCreatingPlan = $request->session()->get('creating_plan', false);

        if ($isCreatingPlan) {
            // If we're already in plan creation mode, continue with createPlan
            Log::info("Continuing with plan creation");
            return $this->createPlan($request);
        }

        // For the orchestrator, only use the last 3 messages (or fewer if there aren't 3 yet)
        $recentMessageCount = min(3, count($allMessages));
        $recentMessages = array_slice($allMessages, -$recentMessageCount);

        Log::info("Recent messages for orchestrator:", ['messages' => $recentMessages]);

        // Prepare the payload for the GPT-4 API with only recent messages
        $payload = [
            'model' => 'gpt-4o-mini-2024-07-18',
            'input' => [
                [
                    'role' => 'system',
                    'content' => 'You are a succession planning AI assistant who only responds to succession planning queries. Your task is to categorize user queries into one of three categories: "Create Plan", "Research", or "Unrelated".

<definitions>
- "Create Plan": Queries that explicitly request creating a succession plan, containing words like "create," "make," "build," "develop," or "prepare" with terms like "plan," "succession," or "talent pipeline."
- "Research": Queries about succession planning, executive talent, leadership development, succession strategies, talent assessment, OR any queries about leadership structures, executive committees (exco), board members, C-suite executives, leadership teams, or company-specific leadership inquiries even if not explicitly mentioning succession planning. These queries are considered related to succession planning because understanding current leadership is fundamental to succession planning work.
- "Unrelated": Any query not directly related to succession planning, executive leadership, talent management, or organizational development.
</definitions>

<relevant_leadership_terms>
- Executive Committee / Exco / ExCom
- Board of Directors / Board Members
- C-Suite / C-Level / Chiefs / CXO
- Leadership Team / Executive Team / Management Team
- Senior Leadership / Top Management
- Organizational Structure / Reporting Lines
- Company-specific leadership (e.g., "Barclays executives", "HSBC board")
</relevant_leadership_terms>

<rules>
- Analyze each message carefully to determine if it\'s related to succession planning or leadership.
- Classify queries about ANY aspect of organizational leadership as "Research" - these are valuable inputs to succession planning.
- Consider company-specific leadership inquiries (e.g., "Who are the exco\'s of Barclays?") as valid "Research" queries.
- Be inclusive rather than exclusive when the query involves leadership, executives, or organization structure.
- For any query mentioning executives, leadership teams, or corporate governance, classify as "Research".
- Never attempt to answer queries classified as "Unrelated".
- Your response must be exactly one of these three flags: "Create Plan", "Research", or "Unrelated".
</rules>

<examples>
Query: "Create a succession plan for our CFO position"
Category: Create Plan

Query: "What are the best practices for executive succession planning?"
Category: Research

Query: "Who are the exco\'s of Barclays?"
Category: Research

Query: "What is the leadership team at Goldman Sachs?"
Category: Research

Query: "Can I get a list of HSBC board members?"
Category: Research

Query: "How do I identify high-potential leadership candidates?"
Category: Research

Query: "Can you write me a marketing email?"
Category: Unrelated

Query: "Generate a Python script to analyze sales data"
Category: Unrelated
</examples>

<thinking>
Let me analyze this query carefully:
1. Is it explicitly requesting creation of a succession plan?
2. Is it asking for information or research about succession planning, executive leadership, or talent management?
3. Or is it completely unrelated to succession planning?

Based on these criteria, I should categorize it as:
</thinking>'
                ],
                ...$recentMessages
            ]
        ];

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . config('ai.openai.api_key'),
            'Content-Type' => 'application/json',
        ])->post('https://api.openai.com/v1/responses', $payload);
        
        Log::info("Response from OpenAI");
        Log::info($response);

        // Check for rate limiting error
        if ($response->status() === 429) {
            Log::warning("OpenAI rate limit hit", ['status' => 429]);
            // Try to queue the request instead of failing
            $payload = [
                // ... build payload as needed for the job
                'model' => 'gpt-4o-mini-2024-07-18',
                'input' => $recentMessages,
                'queue_if_limited' => true,
            ];
            $service = new \App\Services\AI\RateLimitedOpenAiService();
            $queueResult = $service->chatCompletion($payload, null, 0);
            if (is_array($queueResult) && isset($queueResult['queued']) && $queueResult['queued']) {
                return response()->json([
                    'queued' => true,
                    'message' => 'Your request is queued and will be processed as soon as possible.'
                ], 202);
            }
            return response()->json([
                'error' => 'We\'re experiencing high demand. Please wait a moment and try again.',
                'retry_after' => $response->header('Retry-After', 60)
            ], 429);
        }

        // Check for other API errors
        if (!$response->successful()) {
            Log::error("OpenAI API error", [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            return response()->json([
                'error' => 'Unable to process your request. Please try again.'
            ], $response->status());
        }

        $data = $response->json();
        Log::info("Response data:", $data);

        // Get the content from the response
        $content = $data['output'][0]['content'][0]['text'] ?? null;
        Log::info("Content: " . $content);

        // Parse the content
        if ($content === null) {
            Log::info("Could not parse response content");
            return response()->json(['error' => 'Could not parse AI response']);
        }

        $function = $content;
        Log::info("Determined function: " . $function);

        // The function to call to determine which process the user wants to use
        if ($function === "Create Plan") {
            Log::info("Making a plan now");
            $choice = "makePlan";

            // Set a session flag to indicate we're in plan creation mode
            $request->session()->put('creating_plan', true);
            Log::info("Going to create a plan - 2");
            return $this->createPlan($request);
        }
        elseif ($function === "Research") {
            Log::info("Researching now");
            // Make sure to clear the plan creation flag if it was set
            $request->session()->forget('creating_plan');
            Log::info("Going to research function - 3");
            return $this->researchFunction($request);
        }
        elseif ($function === "Unrelated") {
            Log::info("Unrelated query detected");
            $choice = "Unrelated";
            // Keep the session state neutral
            $request->session()->forget('creating_plan');

            $aiResponse = [
                [
                    "type" => "text",
                    "text" => "I'm sorry, but I can only help with succession planning related queries. Please ask me about succession planning, leadership transitions, talent management, or organizational development."
                ]
            ];

            return response()->json([
                'aiResponse' => $aiResponse,
                'choice' => $choice,
                'planCreated' => null,
                'planData' => null,
            ]);
        }
        else {
            $choice = "Undecided";
            // Keep the session state neutral
            $request->session()->forget('creating_plan');

            return response()->json([
                'function' => $function,
                'choice'     => $choice,
                'planCreated' => null,
                'planData' => null,
            ]);
        }
    }

    // ======================================================
    // PLAN CREATION FUNCTIONALITY
    // ======================================================
    
    /**
     * Handles the succession plan creation flow
     * Gathers requirements through conversation and creates database entries
     */
    public function createPlan(Request $request)
    {
        Log::info("The new plan function is has just been triggered");
        $choice = "makePlan";
        $user = auth()->user();

        $userMessage = $request->input('message'); //Getting any new message
        $planData = [];
        $messages = $request->session()->get('messages', []); //Getting the previous messages that have been recieved before this function was triggered
        
        // Add the user's message to the conversation
        $messages[] = [
            'role' => 'user',
            'content' => $userMessage
        ];

        Log::info($messages);

        $planCreated = false;
        
        // Get account and company data
        $accountObj = Account::where('id', $user->account_id)->first();
        $companies = $this->getCompanyData($user, $accountObj);
        $companystring = json_encode($companies);
        
        // Save the updated conversation in the session
        $request->session()->put('messages', $messages);
        Log::info($request->session()->get('messages'));

        $planMsg = $messages;

        // Replace "system" with "user" in $planMsg
        $planMsg = array_map(function ($message) {
            if (isset($message['role']) && $message['role'] === 'system') {
                $message['role'] = 'user';
            }
            return $message;
        }, $planMsg);

        log::info("planMessage", $planMsg);

        // Prepare the payload for the Claude API
        $payload = [
            'model' => config('ai.anthropic.model'),
            'max_tokens' => 2000,
            'system' => $this->getPlanCreationSystemPrompt($companystring),
            'tools' => [
                [
                    'name' => 'create_plan',
                    'description' => 'Creates a succession plan in the system database based on the provided user requirements.',
                    'input_schema' => [
                        'type' => 'object',
                        'properties' => [
                            'plan_name' => [
                                'type' => 'string',
                                'description' => 'The name of the succession plan. This is required.'
                            ],
                            'description' => [
                                'type' => 'string',
                                'description' => "A detailed description of the plan's purpose, generated by the AI. Must be between 80 and 200 words."
                            ],
                            'target_roles' => [
                                'type' => 'array',
                                'items' => [
                                    'type' => 'string'
                                ],
                                'description' => 'An array of roles for which the succession plan is being created. This is required.'
                            ],
                            'minimum_tenure' => [
                                'type' => 'integer',
                                'description' => 'The minimum number of years of experience required for candidates.'
                            ],
                            'step_up_candidates' => [
                                'type' => 'array',
                                'items' => [
                                    'type' => 'string'
                                ],
                                'description' => 'A list of names of internal candidates who could step up into the target roles.'
                            ],
                            'alternative_roles_titles' => [
                                'type' => 'array',
                                'items' => [
                                    'type' => 'string'
                                ],
                                'description' => 'A list of alternative roles titles of selected roles by the user.'
                            ],
                            'acronyms' => [
                                'type' => 'array',
                                'items' => [
                                    'type' => 'string'
                                ],
                                'description' => 'A list of acronyms for the roles titles of selected roles by the user.'
                            ],
                            'companies' => [
                                'type' => 'array',
                                'items' => [
                                    'type' => 'string'
                                ],
                                'description' => 'An array of companies from which external candidates might be sourced.'
                            ],
                            'gender' => [
                                'type' => 'string',
                                'enum' => ['Male', 'Female', 'Not required'],
                                'description' => 'The preferred gender of the candidates.'
                            ],
                            'country' => [
                                'type' => 'array',
                                'items' => [
                                    'type' => 'string'
                                ],
                                'description' => 'The country where the candidates should be located.'
                            ],
                            'is_ethnicity_important' => [
                                'type' => 'boolean',
                                'description' => 'Indicates whether ethnicity is an important consideration in candidate selection.'
                            ],
                            'qualifications' => [
                                'type' => 'array',
                                'items' => [
                                    'type' => 'string'
                                ],
                                'description' => 'A list of required qualifications for the candidates (e.g., degrees, certifications). These are suggested by the AI based on selected roles and companies.'
                            ],
                            'skills' => [
                                'type' => 'array',
                                'items' => [
                                    'type' => 'string'
                                ],
                                'description' => 'A list of required skills for the candidates. These are suggested by the AI based on selected roles and companies. Do not use the word "Experience".'
                            ],
                            'include_alumni' => [
                                'type' => 'boolean',
                                'description' => 'Whether to include people who previously worked at target companies. This must be explicitly set based on user preference.'
                            ]
                        ],
                        'required' => ['plan_name', 'target_roles', 'companies']
                    ]
                ]
            ],
            'messages' => $planMsg
        ];

        try {
            // Call the Claude API
            $response = Http::withHeaders([
                'x-api-key' => $this->anthropicApiKey,
                'anthropic-version' => config('ai.anthropic.version'),
                'Content-Type' => 'application/json',
            ])
            ->timeout(60)
            ->post('https://api.anthropic.com/v1/messages', $payload);

            // Check for rate limiting error
            if ($response->status() === 429) {
                Log::warning("Claude API rate limit hit", ['status' => 429]);
                
                $aiResponse = [
                    [
                        'type' => 'text',
                        'text' => "We're experiencing high demand. Please wait a moment and try again."
                    ]
                ];
                
                return response()->json([
                    'aiResponse' => $aiResponse,
                    'choice' => "makePlan",
                    'planCreated' => false,
                    'planData' => null,
                    'retry_after' => $response->header('Retry-After', 60)
                ], 429);
            }

            // Check for other API errors
            if (!$response->successful()) {
                Log::error("Claude API error", [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                
                $aiResponse = [
                    [
                        'type' => 'text',
                        'text' => "I encountered a technical issue. Please try again in a moment."
                    ]
                ];
                
                return response()->json([
                    'aiResponse' => $aiResponse,
                    'choice' => "makePlan",
                    'planCreated' => false,
                    'planData' => null,
                ], $response->status());
            }

            $data = $response->json();
            Log::info('AI Response after first API call', ['aiResponse' => $data]);

            // Check if there's an error in the response
            if (isset($data['type']) && $data['type'] === 'error') {
                Log::error('Error in Claude API response', ['error' => $data['error'] ?? 'Unknown error']);
                
                // Create a friendly error message for the user
                $aiResponse = [
                    [
                        'type' => 'text',
                        'text' => "I encountered an issue while processing your request. Let's try again. Could you please confirm if you'd like to proceed with creating this plan?"
                    ]
                ];
            } else {
                // Process the normal AI response
                $aiResponse = $data['content'] ?? [];
                Log::info($aiResponse);
            }

            // Add the AI's response to the conversation
            $messages[] = [
                'role' => 'assistant',
                'content' => $aiResponse
            ];

            // Check if a plan was created
            $planCreated = false;
            $toolUseId = null;

            // First check if there are any summaries in recent messages
            $foundSummaryInHistory = false;
            $recentMessagesForCheck = array_slice($messages, -5); // Check last 5 messages
            
            foreach ($recentMessagesForCheck as $msg) {
                if (isset($msg['role']) && $msg['role'] === 'assistant') {
                    // Extract content text whether it's an array or string
                    $msgContent = '';
                    if (is_array($msg['content'])) {
                        foreach ($msg['content'] as $item) {
                            if (isset($item['text'])) {
                                $msgContent .= $item['text'] . " ";
                            }
                        }
                    } else {
                        $msgContent = $msg['content'];
                    }
                    
                    // Check for markers
                    if (strpos($msgContent, "<SUMMARY_START>") !== false && 
                        strpos($msgContent, "</SUMMARY_END>") !== false) {
                        $foundSummaryInHistory = true;
                        Log::info("Summary found in conversation history");
                        break;
                    }
                }
            }
            
            // Look for tool use to create plan
            foreach ($aiResponse as $content) {
                if ($content['type'] === 'tool_use' && $content['name'] === 'create_plan') {
                    // Only allow plan creation if summary was found
                    if ($foundSummaryInHistory) {
                        $planCreated = true;
                        $planData = $content['input'];
                        $toolUseId = $content['id']; // Get the tool_use ID
                        
                        // Log the plan data
                        Log::info('🎯 PLAN DATA EXTRACTED: Successfully extracted plan data from AI response', [
                            'planCreated' => $planCreated,
                            'planData' => $planData,
                            'plan_name' => $planData['plan_name'] ?? 'Unknown',
                            'target_roles' => $planData['target_roles'] ?? [],
                            'companies' => $planData['companies'] ?? [],
                            'user_id' => auth()->id(),
                            'toolUseId' => $toolUseId,
                            'timestamp' => now()->toDateTimeString()
                        ]);
                    } else {
                        // Log the attempt and block plan creation
                        Log::warning('Plan creation attempted without summary', [
                            'planData' => $content['input'] ?? 'No input data',
                            'tool_id' => $content['id'] ?? 'No tool ID',
                        ]);
                        
                        // Don't set planCreated to true, which will block actual plan creation
                    }
                    break;
                }
            }
            
            // We already validated summaries when checking for tool use
            // Just double-check if we need a fallback verification
            if ($planCreated && !$foundSummaryInHistory) {
                Log::info("Using fallback validation for plan creation requirements");
                // Check conversation history for summary as a backup
                $hasSummary = false;
                $recentMessages = array_slice($messages, -10); // Look at last 10 messages
                
                // Debug log entire recent conversation
                Log::debug("Recent conversation for validation:", [
                    'messages' => array_map(function($msg) {
                        return [
                            'role' => $msg['role'],
                            'content_preview' => is_array($msg['content']) ? 'Array content' : substr(is_string($msg['content']) ? $msg['content'] : json_encode($msg['content']), 0, 100) . '...'
                        ];
                    }, $recentMessages)
                ]);
                
                // First check for summary from Claude
                foreach ($recentMessages as $index => $message) {
                    if (isset($message['role']) && $message['role'] === 'assistant') {
                        // Extract content text (could be array or string)
                        $content = '';
                        if (is_array($message['content'])) {
                            foreach ($message['content'] as $contentItem) {
                                if (isset($contentItem['text'])) {
                                    $content .= $contentItem['text'] . ' ';
                                }
                            }
                        } else {
                            $content = $message['content'];
                        }
                        
                        // Look for machine-detectable markers in the content
                        if (strpos($content, "<SUMMARY_START>") !== false && 
                            strpos($content, "</SUMMARY_END>") !== false) {
                            $hasSummary = true;
                            Log::info("Found plan summary with markers in conversation");
                            break;
                        }
                        
                        // Fallback to the old method if markers aren't found
                        // This helps with backward compatibility during transition
                        $summaryPhrases = [
                            "summary of your plan", 
                            "summary of the plan",
                            "summary of the succession plan", 
                            "here's a summary",
                            "plan summary"
                        ];
                        
                        $targetRolePhrases = [
                            "Target Role", 
                            "Target Roles",
                            "target role",
                            "target position",
                            "target job"
                        ];
                        
                        $hasSummaryPhrase = false;
                        foreach ($summaryPhrases as $phrase) {
                            if (strpos($content, $phrase) !== false) {
                                $hasSummaryPhrase = true;
                                break;
                            }
                        }
                        
                        $hasTargetRole = false;
                        foreach ($targetRolePhrases as $phrase) {
                            if (strpos($content, $phrase) !== false) {
                                $hasTargetRole = true;
                                break;
                            }
                        }
                        
                        if ($hasSummaryPhrase && $hasTargetRole) {
                            $hasSummary = true;
                            Log::info("Found plan summary using traditional detection");
                            break;
                        }
                    }
                }
                
                // Then check for user confirmation after finding a summary
                if ($hasSummary) {
                    foreach ($recentMessages as $index => $message) {
                        if (isset($message['role']) && $message['role'] === 'user') {
                            // Check common confirmation phrases
                            $userResponse = strtolower(is_array($message['content']) ? 
                                implode(' ', $message['content']) : $message['content']);
                            
                            // Expanded list of confirmation phrases
                            $confirmationPhrases = [
                                'yes', 'correct', 'looks good', 'good', 'right',
                                'that\'s right', 'proceed', 'confirm', 'ok', 'okay',
                                'approve', 'approved', 'go ahead', 'sure', 'fine',
                                'continue', 'sounds good', 'perfect', 'exactly'
                            ];
                            
                            // Check for exact matches first (for short responses like "yes" or "proceed")
                            if (in_array(trim(strtolower($userResponse)), $confirmationPhrases)) {
                                $hasConfirmation = true;
                                Log::info("Found exact user confirmation for plan summary");
                                break;
                            }
                            
                            // Then check for partial matches within the response
                            foreach ($confirmationPhrases as $phrase) {
                                if (strpos($userResponse, $phrase) !== false) {
                                    $hasConfirmation = true;
                                    Log::info("Found user confirmation for plan summary");
                                    break;
                                }
                            }
                            
                            if ($hasConfirmation) break;
                        }
                    }
                }
                
                // Only require summary - this is a fallback check if our primary summary check failed
                if (!$hasSummary && !$foundSummaryInHistory) {
                    Log::warning("Plan creation attempted without any summary found", [
                        'hasSummary' => $hasSummary,
                        'foundSummaryInHistory' => $foundSummaryInHistory
                    ]);
                    
                    // Create a correction response prompting for a summary
                    $aiResponse = [
                        [
                            "type" => "text",
                            "text" => "<FORCE_SUMMARY>Before creating your plan, I need to provide a detailed summary. Let me summarize what we've discussed so far:</FORCE_SUMMARY>\n\n" .
                                     "Please review the information I'll share and let me know if it accurately reflects your requirements."
                        ]
                    ];
                    
                    // Add response to conversation
                    $messages[] = [
                        'role' => 'assistant',
                        'content' => $aiResponse
                    ];
                    
                    // Save updated conversation
                    $request->session()->put('messages', $messages);
                    
                    // Return response without creating plan
                    return response()->json([
                        'aiResponse' => $aiResponse,
                        'choice' => "makePlan",
                        'planCreated' => false,
                        'planData' => null,
                    ]);
                }
                
                // If summary exists in either primary or fallback check, proceed with plan creation
                Log::info("Validation passed: Summary found, proceeding with plan creation");
            }
            
            Log::info($planCreated);

            // If a plan was created, process and dispatch jobs
            if ($planCreated) {
                // Process the plan using the plan processor service
                $result = $this->planProcessor->processPlan($planData, $user);
                
                if ($result['success']) {
                    // Get the plan record
                    $plan = $result['plan'];
                    $planData = $result['plan_data'];
                    // Generate the job description using the plan data
                    $getJobDescriptionResponse = $this->generateJobDescription($planData);

                    // Extract the job description text from the response
                    $jobDescription = null;
                    if ($getJobDescriptionResponse) {
                        $data = $getJobDescriptionResponse->getData(true);
                        $jobDescription = $data['job_description'] ?? '';
                    }
                    
                    // Update the job_description column in the SuccessionPlan table
                    if ($jobDescription && isset($plan->id)) {
                        SuccessionPlan::where('id', $plan->id)->update(['job_description' => $jobDescription]);
                    }
               
                    // Create a success response message
                    $includeAlumniText = isset($planData['include_alumni']) && !$planData['include_alumni'] 
                        ? " The search will focus on current employees at your target companies." 
                        : " The search will include both current employees and alumni who left your target companies within the last 5 years.";
                    
                    $aiResponse = [
                        [
                            "type" => "text",
                            "text" => "The '<b>{$planData['plan_name']}</b>' has been successfully created, focusing on strategic succession planning for the suggested role." . $includeAlumniText
                        ]
                    ];
                    
                    // Add the AI's response to the conversation
                    $messages[] = [
                        'role' => 'assistant',
                        'content' => $aiResponse
                    ];
                    
                    // Reset the conversation state
                    $planCreated = false;
                    $request->session()->put('creating_plan', false);
                    $request->session()->forget('messages');
                    
                    Log::info("Conversation messages cleared from session after plan creation");
                    
                    // Dispatch the search job
                    $this->dispatchSearchJob($planData, $user);
                    
                    return response()->json([
                        'aiResponse' => $aiResponse,
                        'choice' => "makePlan",
                        'planCreated' => $planCreated,
                        'planData' => $planData,
                    ]);
                } else {
                    // Return error response
                    $errorMessage = "I encountered an issue while creating the plan. Please try again.";
                    $aiResponse = [
                        [
                            'type' => 'text',
                            'text' => $errorMessage,
                        ]
                    ];
                    
                    return response()->json([
                        'aiResponse' => $aiResponse,
                        'choice' => "makePlan",
                        'planCreated' => false,
                        'planData' => null,
                    ]);
                }
            } else {
                // No plan created yet, continue the conversation
                Log::info('AI Response after second API call', ['aiResponse' => $data]);

                $aiResponse = $data['content'];

                // Add the AI's response to the conversation
                $messages[] = [
                    'role' => 'assistant',
                    'content' => $aiResponse
                ];

                // Save the updated conversation in the session
                $request->session()->put('messages', $messages);
                
                Log::info("💬 CHAT CONVERSATION: Updated conversation stored in session", [
                    'user_id' => auth()->id(),
                    'message_count' => count($messages),
                    'last_message_role' => $messages[count($messages) - 1]['role'] ?? 'unknown',
                    'session_id' => $request->session()->getId()
                ]);

                // Log the final plan data and planCreated flag
                Log::info('Final planCreated and planData', [
                    'planCreated' => $planCreated,
                    'planData' => $planData,
                ]);

                // Return a JSON response for AJAX
                return response()->json([
                    'aiResponse' => $aiResponse,
                    'choice' => "makePlan",
                    'planCreated' => $planCreated,
                    'planData' => $planData,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error in sendMessage', ['exception' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            
            // Check if the exception is related to tool use issues
            $isToolError = strpos($e->getMessage(), 'tool_use') !== false || 
                           strpos($e->getMessage(), 'tool_result') !== false;
            
            if ($isToolError && !empty($planData)) {
                // The plan was likely created but there was an issue with tool handling
                $problem = "I have created the plan but ran into difficulties showing the confirmation. Please check your Plans page where it should be the first entry.";
            } else {
                // Generic error handling
                $problem = "I'm sorry, I encountered an unexpected issue. Please try again or refresh the page.";
            }
                
            $aiResponse = [
                [
                    'type' => 'text',
                    'text' => $problem,
                ]
            ];

            return response()->json([
                'aiResponse' => $aiResponse,
                'planCreated' => !empty($planData),
                'planData' => $planData,
            ]);
        }
    }

    /**
     * Get the system prompt for plan creation conversation
     * 
     * @param string $companyString JSON string of available companies
     * @return string The system prompt
     */
    protected function getPlanCreationSystemPrompt($companyString)
    {
        return <<<'EOT'
You're Sam, a succession planning expert who specializes in executive talent acquisition. Be friendly and conversational while guiding users through the planning process.

<communication_style>
- Be concise and direct - keep all responses under 100 words
- Use very brief greetings and get straight to the point
- Ask exactly ONE question at a time and wait for response
- Present options in bulleted lists without explanations
- Use simple, direct language
- Never combine multiple questions in a single message
- Complete one step fully before moving to the next
</communication_style>

<objective>
Gather six critical pieces of information in a conversational way:
1. Target role(s) for succession planning
2. Companies or industry focus
3. Step-up candidate consideration
4. Minimum tenure requirements
5. Gender preferences (if any)
6. Geographic/country preferences (if any)
</objective>

<conversation_guidelines>
- CRITICAL: Ask exactly ONE question in each response - never more
- After user responds, acknowledge briefly and move to next step
- Present options as simple bullet lists without descriptions
- Keep all responses under 100 words maximum
- Follow the sequence: role → companies → step-up → tenure → gender → location
- Never skip ahead or combine steps, even if user provides multiple answers
- Do not provide explanations unless explicitly asked
</conversation_guidelines>

<process>
<location_handling>
1. When a location is mentioned:
   - First, check if it's a city that maps to a country in our list
   - If not, check if it's a country that needs standardization
   - If still not found, ask the user to specify the country
2. Always use the standardized country name in the final plan
3. If multiple cities in the same country are mentioned, consolidate to a single country entry
4. If cities from different countries are mentioned, include all relevant countries
</location_handling>

<target_roles>
When user mentions a role:
1. <thinking>
Consider seniority level, function area, similar roles, alternatives. If they mention an abbreviation (like CEO, COO, CFO), include the full form. If they mention full form, include common abbreviations.
</thinking>
2. List 10 relevant alternative roles with brief descriptions, including both abbreviated and full forms:
   1. Role A - Brief description of responsibilities/focus
   2. Role B - Brief description of responsibilities/focus
3. For abbreviated roles (CEO, COO, etc.), automatically include both forms in the final target_roles array
4. Ask: "Would you like to proceed with [original role] or would you prefer one of these alternatives? I can provide more details on any of them."
5. Accept any selection format and acknowledge their choice
6. Provide a brief explanation of why their chosen role is important for succession planning
7. Move to companies only after role selection is complete
</target_roles>

<companies>
After role selection:
1. <thinking>
Identify 8-10 major companies in the industry without descriptions.
Use standard global company names WITHOUT regional suffixes.
</thinking>
2. Present a simple bullet list of 8-10 companies without descriptions
3. CRITICAL: Use only standard global company names (Samsung, Apple, Sony) - never add country suffixes
4. Ask ONLY: "Which companies would you like to include?"
5. Accept selections and move immediately to next step
</companies>

<step_up_candidates>
After company selection:
1. <thinking>Identify 5-8 potential step-up roles without descriptions</thinking>
2. Ask ONLY: "Would you like to include step-up candidates? Yes/No"
3. If yes, list 5-8 potential step-up roles as bullet points without descriptions
4. If no, use ["none"] internally
5. Move immediately to next step
</step_up_candidates>

<minimum_tenure>
After step-up discussion:
1. <thinking>Executive roles typically require 5-10 years of experience</thinking>
2. Ask ONLY: "How many years of minimum experience should candidates have?"
3. Accept any numerical input
4. Move immediately to next step
</minimum_tenure>

<preferences>
After minimum tenure discussion:
1. Ask ONLY: "Do you have any gender preferences? (Male/Female/None)"
2. Accept answer without elaboration
3. Ask ONLY: "Which countries or regions should we focus on?"
4. Accept answer without elaboration
5. Ask ONLY: "Would you like to include people who previously worked at your target companies within the last 5 years? Yes/No"
6. Accept answer and proceed to summary
</preferences>

<country_standardization>
When users mention location names, follow these rules:

1. For cities, always convert to their respective countries using this mapping:
   - "New York", "Los Angeles", "Chicago", "San Francisco" → "United States"
   - "London", "Manchester", "Birmingham" → "United Kingdom"
   - "Toronto", "Vancouver", "Montreal" → "Canada"
   - "Sydney", "Melbourne" → "Australia"
   - "Berlin", "Munich", "Hamburg" → "Germany"
   - "Paris", "Marseille", "Lyon" → "France"
   - "Tokyo", "Osaka", "Kyoto" → "Japan"
   - "Mumbai", "Delhi", "Bangalore" → "India"
   - "Shanghai", "Beijing" → "China"
   - "Dubai", "Abu Dhabi" → "United Arab Emirates"
   - "Johannesburg", "Cape Town" → "South Africa"
   - "Auckland", "Wellington" → "New Zealand"
   - "Singapore" → "Singapore"
   - "Hong Kong" → "China"
   - "Sao Paulo", "Rio de Janeiro" → "Brazil"
   - "Moscow", "Saint Petersburg" → "Russia"

2. For country names, standardize them as follows:
   - "UK", "U.K.", "Britain", "Great Britain", "England", "Scotland", "Wales", "Northern Ireland" → "United Kingdom"
   - "US", "U.S.", "USA", "U.S.A.", "America", "United States" → "United States"
   - "Czech Republic", "Czech", "Czechia", "Czech Rep", "CR" → "Czechia"
   - "UAE", "U.A.E.", "Emirates" → "United Arab Emirates"
   - "SA", "South Africa", "RSA" → "South Africa"
   - "NZ", "New Zealand" → "New Zealand"
   - "Australia", "AU", "AUS" → "Australia"
   - "Canada", "CA", "CAN" → "Canada"
   - "Germany", "DE", "Deutschland" → "Germany"
   - "France", "FR", "République française" → "France"
   - "Japan", "JP", "日本" → "Japan"
   - "China", "CN", "PRC", "People's Republic of China" → "China"
   - "India", "IN", "भारत" → "India"

3. If a city is not in the list above, ask the user to specify the country.

4. Always use the standardized country names in the final plan and when calling create_plan.
</country_standardization>
</process>

<summary_and_completion>
After gathering all information:
1. YOU MUST ALWAYS present a friendly summary with this format:
   - Target Role: [Original role first, then any others, separated by commas]
   - Companies: [List company names WITHOUT country suffixes, with brief context where helpful]
   - Step-up candidates: [Yes/No + roles if Yes, with brief explanation]
   - Minimum Tenure: [Number of years required experience]
   - Gender preference: [Male/Female/None, with context]
   - Location: [Countries/regions with brief context]
   - Include alumni: [Yes/No - explain as "will search for people who previously worked at target companies within the last 5 years" or "will only search for current employees at target companies"]
2. ALWAYS begin the summary with: "<SUMMARY_START>Here\'s a summary of the succession plan we\'ve created together:"
3. ALWAYS end the summary section with: "</SUMMARY_END> Is this accurate? I can adjust before finalizing."
4. After receiving any user response to the summary, write a conversational plan description of 80-120 words that explains the benefits of this plan
5. ONLY THEN call create_plan function with target_roles starting with the original role
6. CRITICAL: Never call create_plan without first providing a summary with the required <SUMMARY_START> and </SUMMARY_END> markers.
7. You MUST verify that you've received explicit user confirmation of the summary before calling create_plan.
8. If you've gathered all required information but haven't provided a summary yet, ALWAYS generate a summary before proceeding.
9. NEVER repeat yourself - each response should contain unique content.
10. If you notice your previous message was sent twice, acknowledge it with "Sorry for the duplicate message" and continue.
</summary_and_completion>

<payload_construction>
'tools' => [
    [
        'name' => 'create_plan',
        'description' => 'Creates a succession plan in the system database based on the provided user requirements.',
        'input_schema' => [
            'type' => 'object',
            'properties' => [
                'plan_name' => [
                    'type' => 'string',
                    'description' => 'The name of the succession plan. This is required.'
                ],
                'description' => [
                    'type' => 'string',
                    'description' => "A detailed description of the plan\'s purpose, generated by the AI. Must be between 80 and 200 words."
                ],
                'target_roles' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'string'
                    ],
                    'description' => 'An array of roles for which the succession plan is being created. This is required.'
                ],
                'minimum_tenure' => [
                    'type' => 'integer',
                    'description' => 'The minimum number of years of experience required for candidates.'
                ],
                'step_up_candidates' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'string'
                    ],
                    'description' => 'A list of names of internal candidates who could step up into the target roles.'
                ],
                'alternative_roles_titles' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'string'
                    ],
                    'description' => 'A list of alternative roles titles of selected roles by the user.'
                ],
                'acronyms' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'string'
                    ],
                    'description' => 'A list of acronyms for the roles titles of selected roles by the user.'
                ],
                'companies' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'string'
                    ],
                    'description' => 'An array of companies from which external candidates might be sourced.'
                ],
                'gender' => [
                    'type' => 'string',
                    'enum' => ['Male', 'Female', 'Not required'],
                    'description' => 'The preferred gender of the candidates.'
                ],
                'country' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'string'
                    ],
                    'description' => 'The country where the candidates should be located.'
                ],
                'is_ethnicity_important' => [
                    'type' => 'boolean',
                    'description' => 'Indicates whether ethnicity is an important consideration in candidate selection.'
                ],
                'qualifications' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'string'
                    ],
                    'description' => 'A list of required qualifications for the candidates (e.g., degrees, certifications). These are suggested by the AI based on selected roles and companies.'
                ],
                'skills' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'string'
                    ],
                    'description' => 'A list of required skills for the candidates. These are suggested by the AI based on selected roles and companies. Do not use the word "Experience".'
                ],
                'include_alumni' => [
                    'type' => 'boolean',
                    'description' => 'Whether to include people who previously worked at target companies. This must be explicitly set based on user preference.'
                ]
            ],
            'required' => ['plan_name', 'target_roles', 'companies']
        ]
    ]
],
'messages' => $planMsg
EOT;
    }

    /**
     * Get company data based on user's account preferences
     * 
     * @param object $user The current user
     * @param object $accountObj The user's account
     * @return array Companies data
     */
    protected function getCompanyData($user, $accountObj)
    {
        $interestedCompanies = false;
        $interestedIndustries = false;
        $interestedSectors = false;
        
        if ($accountObj) {
            if ($accountObj->company_of_interest && $accountObj->company_of_interest != "") {
                $interestedCompanies = explode(",", $accountObj->company_of_interest);
            }
            else if ($accountObj->industry_interest && $accountObj->industry_interest != "") {
                $interestedIndustries = explode(",", $accountObj->industry_interest);
            }
            else if ($accountObj->sector_interest && $accountObj->sector_interest != "") {
                $interestedSectors = explode(",", $accountObj->sector_interest);
            }
        }

        $companyIds = [];
        if ($interestedCompanies && count($interestedCompanies) > 0) {
            $companyIds = $interestedCompanies;
        }
        else if ($interestedIndustries && count($interestedIndustries) > 0) {
            $companyIds = Company::whereIn('industry', $interestedIndustries)->pluck('id')->toArray();
        }
        else if ($interestedSectors && count($interestedSectors) > 0) {
            $companyIds = Company::whereIn('sector', $interestedSectors)->pluck('id')->toArray();
        }

        return Company::select(['id', 'name', 'industry', 'sector'])
            ->where('id', '!=', $user->company_id)
            ->when(!empty($companyIds), function ($query) use ($companyIds) {
                $query->whereIn('id', $companyIds);
            })
            ->limit(15)
            ->get()->map(function ($company) {
                return [
                    'value' => $company->name,
                    'label' => $company->name,
                    'industry' => $company->industry,
                    'sector' => $company->sector
                ];
            })->toArray();
    }

    /**
     * Dispatch a job to search for candidates
     * 
     * @param array $planData The plan data
     * @param object $user The current user
     */
    protected function dispatchSearchJob($planData, $user)
    {
        Log::info("Dispatching search job for plan ID {$planData['plan_id']}");
        
        // Dispatch the unified search job
        $job = new SearchCandidatesJob($planData, $user, SearchCandidatesJob::SEARCH_BOTH);
        dispatch($job);
        
        Log::info("Search job dispatched successfully");
    }

    // ======================================================
    // RESEARCH FUNCTIONALITY
    // ======================================================
    
    /**
     * Handles research queries using ResearchFunctionality service
     * Searches for and summarizes information based on user questions
     */
    public function researchFunction(Request $request)
    {
        Log::info("Into research function");

        $user = auth()->user();
        $userMessage = $request->input('message');
        Log::info("userMessage in research function: " . $userMessage);
        
        // Get previous messages for context
        $messages = $request->session()->get('messages', []);

        // Perform research using the service
        $aiResponse = $this->researchService->performResearch($userMessage, $messages);
        
        // Extract the response message
        $responseMessage = $aiResponse[0]['text'] ?? "I couldn't find information on that topic.";
        
        // Save the response to the session
        $messages[] = ['role' => 'assistant', 'content' => $responseMessage];
        $request->session()->put('messages', $messages);

        Log::info('Final AI Response', ['formattedResponse' => $aiResponse]);

        return response()->json([
            'aiResponse'  => $aiResponse,
            'choices'     => 'Research',
            'planCreated' => null,
            'planData'    => null,
        ]);
    }

public function generateJobDescription($data)
{     
    // Build the prompt
    $prompt = "Generate a professional job description for the position of " . implode(" / ", $data['target_roles']) . ". 
Company: " . implode(", ", $data['companies']) . ". 
Location: " . implode(", ", $data['country']) . ". 
Plan Focus: " . $data['description'] . " 
Ideal candidates are " . $data['gender'] . " with a minimum of " . $data['minimum_tenure'] . " years of experience. 
Preferred qualifications include: " . implode(", ", $data['qualifications']) . ". 
Key skills: " . implode(", ", $data['skills']) . ". 
Internal step-up candidates may come from the role of " . implode(", ", $data['step_up_candidates']) . ".";

    

    $apiKey = env('OPENAI_API_KEY');
    $response = Http::withHeaders([
        'Authorization' => 'Bearer ' . $apiKey,
        'Content-Type' => 'application/json',
    ])->post('https://api.openai.com/v1/chat/completions', [
        'model' => 'gpt-4o',
        'messages' => [
            ['role' => 'system', 'content' => 'You are an expert HR assistant and job description generator.'],
            ['role' => 'user', 'content' => $prompt],
        ],
        'temperature' => 0.7,
    ]);

    // Check for API errors
    if ($response->failed()) {
        return response()->json([
            'error' => 'OpenAI API request failed.',
            'status' => $response->status(),
            'details' => $response->body(),
        ], $response->status());
    }
    $result = $response->json();
    return response()->json([
        'job_description' => $result['choices'][0]['message']['content'] ?? 'No response generated.',
    ]);
}
}