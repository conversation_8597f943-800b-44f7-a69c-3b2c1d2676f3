<?php

namespace App\Services;

use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Collection;
use App\Jobs\SearchCandidatesJob;
use App\Jobs\ExternalSearchPerplexityJob;
use App\Jobs\CreateSuccessionPlan;
use App\Models\SuccessionPlan;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class BatchService
{
    /**
     * Create a succession plan using batch processing
     */
    public function createSuccessionPlanBatch(SuccessionPlan $plan, User $user): Batch
    {
        $jobs = collect([
            new SearchCandidatesJob($plan, $user),
            new ExternalSearchPerplexityJob($plan),
            new CreateSuccessionPlan($plan, $user)
        ]);

        return Bus::batch($jobs->toArray())
            ->name("succession-plan-{$plan->id}")
            ->then(function (Batch $batch) use ($plan) {
                Log::info("Succession plan batch completed", [
                    'plan_id' => $plan->id,
                    'batch_id' => $batch->id,
                    'processed_jobs' => $batch->processedJobs(),
                    'total_jobs' => $batch->totalJobs,
                ]);
                
                $plan->update([
                    'status' => 'completed',
                    'completed_at' => Carbon::now()
                ]);
            })
            ->catch(function (Batch $batch, \Throwable $e) use ($plan) {
                Log::error("Succession plan batch failed", [
                    'plan_id' => $plan->id,
                    'batch_id' => $batch->id,
                    'error' => $e->getMessage(),
                    'failed_jobs' => $batch->failedJobs,
                ]);
                
                $plan->update([
                    'status' => 'failed',
                    'error_message' => $e->getMessage()
                ]);
            })
            ->finally(function (Batch $batch) use ($plan) {
                Log::info("Succession plan batch finished", [
                    'plan_id' => $plan->id,
                    'batch_id' => $batch->id,
                    'finished_at' => Carbon::now()
                ]);
            })
            ->allowFailures()
            ->onConnection('redis')
            ->onQueue('high')
            ->dispatch();
    }

    /**
     * Create parallel candidate search batch
     */
    public function parallelCandidateSearch(SuccessionPlan $plan, array $searchCriteria): Batch
    {
        $jobs = collect($searchCriteria)->map(function ($criteria) use ($plan) {
            return new SearchCandidatesJob($plan, auth()->user(), $criteria);
        });

        return Bus::batch($jobs->toArray())
            ->name("parallel-search-{$plan->id}")
            ->then(function (Batch $batch) use ($plan) {
                // Aggregate results from all parallel searches
                $this->aggregateSearchResults($plan, $batch);
            })
            ->catch(function (Batch $batch, \Throwable $e) use ($plan) {
                Log::error("Parallel search batch failed", [
                    'plan_id' => $plan->id,
                    'error' => $e->getMessage()
                ]);
            })
            ->allowFailures()
            ->onConnection('redis')
            ->onQueue('search')
            ->dispatch();
    }

    /**
     * Process bulk succession plans with progress tracking
     */
    public function processBulkPlans(Collection $plans, User $user): Batch
    {
        $jobs = $plans->map(function ($plan) use ($user) {
            return new CreateSuccessionPlan($plan, $user);
        });

        return Bus::batch($jobs->toArray())
            ->name("bulk-succession-plans-" . Carbon::now()->format('Y-m-d-H-i'))
            ->then(function (Batch $batch) {
                Log::info("Bulk succession plans completed", [
                    'batch_id' => $batch->id,
                    'total_processed' => $batch->processedJobs()
                ]);
            })
            ->progress(function (Batch $batch) {
                // Update progress in cache for real-time UI updates
                cache()->put(
                    "batch_progress_{$batch->id}",
                    [
                        'processed' => $batch->processedJobs(),
                        'total' => $batch->totalJobs,
                        'percentage' => round(($batch->processedJobs() / $batch->totalJobs) * 100, 2),
                        'failed' => $batch->failedJobs,
                    ],
                    3600 // 1 hour
                );
            })
            ->allowFailures()
            ->onConnection('redis')
            ->onQueue('bulk')
            ->dispatch();
    }

    /**
     * Retry failed jobs in a batch
     */
    public function retryFailedBatchJobs(string $batchId): bool
    {
        $batch = Bus::findBatch($batchId);
        
        if (!$batch) {
            return false;
        }

        if ($batch->failedJobs > 0) {
            $batch->add($batch->failedJobIds()->map(function ($jobId) {
                // Re-create job based on failed job data
                return $this->recreateJobFromFailedJob($jobId);
            })->filter()->toArray());
            
            return true;
        }

        return false;
    }

    /**
     * Get batch progress for UI
     */
    public function getBatchProgress(string $batchId): ?array
    {
        return cache()->get("batch_progress_{$batchId}");
    }

    /**
     * Cancel a running batch
     */
    public function cancelBatch(string $batchId): bool
    {
        $batch = Bus::findBatch($batchId);
        
        if ($batch && !$batch->finished()) {
            $batch->cancel();
            return true;
        }

        return false;
    }

    /**
     * Private method to aggregate search results
     */
    private function aggregateSearchResults(SuccessionPlan $plan, Batch $batch): void
    {
        // Implementation for aggregating parallel search results
        Log::info("Aggregating search results for plan {$plan->id}");
    }

    /**
     * Private method to recreate job from failed job
     */
    private function recreateJobFromFailedJob(string $jobId): ?object
    {
        // Implementation to recreate job from failed job data
        // This would need to query the failed_jobs table and reconstruct the job
        return null;
    }
}
