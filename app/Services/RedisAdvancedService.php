<?php

namespace App\Services;

use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class RedisAdvancedService
{
    /**
     * Batch operations using Redis pipelines for better performance
     */
    public function batchJobMetrics(array $metrics): bool
    {
        try {
            Redis::pipeline(function ($pipe) use ($metrics) {
                foreach ($metrics as $key => $value) {
                    // Use pipeline to batch multiple Redis operations
                    $pipe->hset('job_metrics', $key, json_encode($value));
                    $pipe->expire("job_metrics:$key", 3600); // 1 hour TTL
                    
                    // Add to sorted set for time-series data
                    $pipe->zadd('job_metrics_timeline', time(), $key);
                    
                    // Increment counters
                    if (isset($value['status'])) {
                        $pipe->incr("job_count:{$value['status']}");
                    }
                }
            });
            
            Log::info('Batch job metrics updated via Redis pipeline', [
                'metrics_count' => count($metrics)
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to batch job metrics', [
                'error' => $e->getMessage(),
                'metrics_count' => count($metrics)
            ]);
            
            return false;
        }
    }

    /**
     * Atomic queue operations using Redis transactions
     */
    public function atomicQueueOperation(string $sourceQueue, string $targetQueue, int $maxJobs = 10): int
    {
        $movedJobs = 0;
        
        try {
            // Use Redis transaction with optimistic locking
            Redis::watch("queues:$sourceQueue");
            
            $sourceSize = Redis::llen("queues:$sourceQueue");
            
            if ($sourceSize > 0) {
                $jobsToMove = min($maxJobs, $sourceSize);
                
                Redis::multi();
                
                // Move jobs atomically
                for ($i = 0; $i < $jobsToMove; $i++) {
                    Redis::rpoplpush("queues:$sourceQueue", "queues:$targetQueue");
                }
                
                // Update counters
                Redis::incrby("queue_stats:$sourceQueue:moved_out", $jobsToMove);
                Redis::incrby("queue_stats:$targetQueue:moved_in", $jobsToMove);
                Redis::set("queue_stats:last_move", Carbon::now()->toDateTimeString());
                
                $results = Redis::exec();
                
                if ($results !== null) {
                    $movedJobs = $jobsToMove;
                    Log::info("Atomically moved jobs between queues", [
                        'source' => $sourceQueue,
                        'target' => $targetQueue,
                        'moved_jobs' => $movedJobs
                    ]);
                } else {
                    Log::warning("Atomic queue operation failed due to concurrent modification", [
                        'source' => $sourceQueue,
                        'target' => $targetQueue
                    ]);
                }
            }
        } catch (\Exception $e) {
            Log::error('Atomic queue operation failed', [
                'source' => $sourceQueue,
                'target' => $targetQueue,
                'error' => $e->getMessage()
            ]);
        }
        
        return $movedJobs;
    }

    /**
     * Advanced queue monitoring with Redis streams
     */
    public function logQueueEvent(string $event, array $data): string
    {
        try {
            $streamKey = 'queue_events';
            $eventId = Redis::xadd($streamKey, '*', array_merge([
                'event' => $event,
                'timestamp' => Carbon::now()->toDateTimeString(),
            ], $data));
            
            // Trim stream to keep only last 10000 events
            Redis::xtrim($streamKey, 10000, true);
            
            Log::debug("Queue event logged to Redis stream", [
                'event' => $event,
                'event_id' => $eventId,
                'data' => $data
            ]);
            
            return $eventId;
        } catch (\Exception $e) {
            Log::error('Failed to log queue event to Redis stream', [
                'event' => $event,
                'error' => $e->getMessage()
            ]);
            
            return '';
        }
    }

    /**
     * Read queue events from Redis stream
     */
    public function readQueueEvents(int $count = 100, string $lastId = '0'): array
    {
        try {
            $events = Redis::xread(['queue_events' => $lastId], $count);
            
            $formattedEvents = [];
            if (isset($events['queue_events'])) {
                foreach ($events['queue_events'] as $eventId => $eventData) {
                    $formattedEvents[] = [
                        'id' => $eventId,
                        'data' => $eventData,
                        'timestamp' => $eventData['timestamp'] ?? null
                    ];
                }
            }
            
            return $formattedEvents;
        } catch (\Exception $e) {
            Log::error('Failed to read queue events from Redis stream', [
                'error' => $e->getMessage()
            ]);
            
            return [];
        }
    }

    /**
     * Implement distributed locking for critical operations
     */
    public function acquireDistributedLock(string $resource, int $ttl = 30): ?string
    {
        $lockKey = "lock:$resource";
        $lockValue = uniqid(gethostname() . '_', true);
        
        try {
            // Use SET with NX (only if not exists) and EX (expiration)
            $acquired = Redis::set($lockKey, $lockValue, 'EX', $ttl, 'NX');
            
            if ($acquired) {
                Log::debug("Distributed lock acquired", [
                    'resource' => $resource,
                    'lock_value' => $lockValue,
                    'ttl' => $ttl
                ]);
                
                return $lockValue;
            }
            
            return null;
        } catch (\Exception $e) {
            Log::error('Failed to acquire distributed lock', [
                'resource' => $resource,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * Release distributed lock safely
     */
    public function releaseDistributedLock(string $resource, string $lockValue): bool
    {
        $lockKey = "lock:$resource";
        
        try {
            // Use Lua script to ensure we only delete the lock if we own it
            $luaScript = '
                if redis.call("GET", KEYS[1]) == ARGV[1] then
                    return redis.call("DEL", KEYS[1])
                else
                    return 0
                end
            ';
            
            $result = Redis::eval($luaScript, 1, $lockKey, $lockValue);
            
            if ($result === 1) {
                Log::debug("Distributed lock released", [
                    'resource' => $resource,
                    'lock_value' => $lockValue
                ]);
                
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('Failed to release distributed lock', [
                'resource' => $resource,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Advanced queue statistics using Redis hyperLogLog
     */
    public function trackUniqueJobsProcessed(string $jobClass): bool
    {
        try {
            $key = "unique_jobs:" . date('Y-m-d');
            Redis::pfadd($key, [$jobClass . ':' . time()]);
            Redis::expire($key, 86400 * 7); // Keep for 7 days
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to track unique jobs processed', [
                'job_class' => $jobClass,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Get estimated unique jobs processed today
     */
    public function getUniqueJobsProcessedToday(): int
    {
        try {
            $key = "unique_jobs:" . date('Y-m-d');
            return Redis::pfcount($key);
        } catch (\Exception $e) {
            Log::error('Failed to get unique jobs count', [
                'error' => $e->getMessage()
            ]);
            
            return 0;
        }
    }

    /**
     * Implement sliding window rate limiting
     */
    public function isRateLimited(string $key, int $maxRequests, int $windowSize = 60): bool
    {
        try {
            $now = time();
            $window = $now - $windowSize;
            
            // Remove expired entries and count current requests
            Redis::zremrangebyscore($key, 0, $window);
            $currentRequests = Redis::zcard($key);
            
            if ($currentRequests >= $maxRequests) {
                return true;
            }
            
            // Add current request
            Redis::zadd($key, $now, uniqid());
            Redis::expire($key, $windowSize);
            
            return false;
        } catch (\Exception $e) {
            Log::error('Rate limiting check failed', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            
            // Fail open (allow request) on error
            return false;
        }
    }

    /**
     * Bulk operations using Redis Lua scripts for atomicity
     */
    public function bulkUpdateJobStatuses(array $jobUpdates): bool
    {
        try {
            $luaScript = '
                local updates = cjson.decode(ARGV[1])
                local success_count = 0
                
                for i, update in ipairs(updates) do
                    local job_id = update.job_id
                    local status = update.status
                    local timestamp = update.timestamp
                    
                    -- Update job status
                    redis.call("HSET", "job_status:" .. job_id, 
                        "status", status,
                        "updated_at", timestamp
                    )
                    
                    -- Add to status index
                    redis.call("SADD", "jobs_by_status:" .. status, job_id)
                    
                    -- Update metrics
                    redis.call("INCR", "job_count:" .. status)
                    
                    success_count = success_count + 1
                end
                
                return success_count
            ';
            
            $result = Redis::eval($luaScript, 0, json_encode($jobUpdates));
            
            Log::info('Bulk job status update completed', [
                'updates_requested' => count($jobUpdates),
                'updates_completed' => $result
            ]);
            
            return $result === count($jobUpdates);
        } catch (\Exception $e) {
            Log::error('Bulk job status update failed', [
                'updates_count' => count($jobUpdates),
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Memory-efficient queue cleanup using scan
     */
    public function cleanupExpiredKeys(int $batchSize = 1000): int
    {
        $cleaned = 0;
        $cursor = 0;
        
        try {
            do {
                // Use SCAN to iterate through keys without blocking Redis
                $result = Redis::scan($cursor, [
                    'match' => 'job_*',
                    'count' => $batchSize
                ]);
                
                if ($result !== false) {
                    [$cursor, $keys] = $result;
                    
                    if (!empty($keys)) {
                        // Check TTL and clean up expired keys
                        Redis::pipeline(function ($pipe) use ($keys, &$cleaned) {
                            foreach ($keys as $key) {
                                $ttl = Redis::ttl($key);
                                if ($ttl === -1) { // No expiration set
                                    // Set reasonable expiration based on key type
                                    if (strpos($key, 'job_temp_') === 0) {
                                        $pipe->expire($key, 3600); // 1 hour for temp
                                    } elseif (strpos($key, 'job_result_') === 0) {
                                        $pipe->expire($key, 86400); // 1 day for results
                                    } elseif (strpos($key, 'job_error_') === 0) {
                                        $pipe->expire($key, 604800); // 1 week for errors
                                    }
                                    $cleaned++;
                                }
                            }
                        });
                    }
                }
            } while ($cursor !== 0);
            
            Log::info('Redis key cleanup completed', [
                'keys_processed' => $cleaned
            ]);
            
            return $cleaned;
        } catch (\Exception $e) {
            Log::error('Redis key cleanup failed', [
                'error' => $e->getMessage(),
                'keys_cleaned' => $cleaned
            ]);
            
            return $cleaned;
        }
    }
}
