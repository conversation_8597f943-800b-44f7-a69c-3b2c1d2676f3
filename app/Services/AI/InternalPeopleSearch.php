<?php

namespace App\Services\AI;

use App\Models\CareerHistories;
use App\Models\People;
use App\Models\InternalPeople;
use App\Models\Skills;
use App\Models\pipeline;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class InternalPeopleSearch
{
    /**
     * Search and evaluate internal candidates based on plan requirements
     * 
     * @param array $planData The succession plan data
     * @param object $user The current user
     * @return array The candidates found and their scores
     */
    public function searchInternalCandidates($planData, $user)
    {
        // Normalize search parameters
        $params = $this->normalizeSearchParams($planData);
        
        // Log the search parameters
        Log::info("🔍 INTERNAL SEARCH: Starting search with parameters", [
            'plan_id' => $planData['plan_id'] ?? 'unknown',
            'plan_name' => $planData['plan_name'] ?? 'unknown',
            'target_roles' => $params['roles'] ?? [],
            'companies' => $params['companies'] ?? [],
            'include_alumni' => $params['include_alumni'] ?? 'not set',
            'countries' => $params['countries'] ?? [],
            'gender' => $params['gender'] ?? 'not specified',
            'minimum_tenure' => $params['minimum_tenure'] ?? 'not specified'
        ]);
        
        // Find external candidates (from People table)
        $externalCandidates = $this->findExternalCandidates($params, $user);
        Log::info("📊 External candidates found: " . $externalCandidates->count());
        
        // Find internal candidates (from InternalPeople table)
        $internalCandidates = $this->findInternalCandidates($params, $user);
        Log::info("📊 Internal candidates found: " . $internalCandidates->count());
        
        // Combine and score all candidates
        $allCandidates = $externalCandidates->concat($internalCandidates);
        Log::info("📊 Total candidates before scoring: " . $allCandidates->count());
        
        $scoredCandidates = $this->scoreAllCandidates($allCandidates, $params, $planData);
        Log::info("📊 Candidates after scoring: " . $scoredCandidates->count());

        // Filter by minimum experience in target role (across all history)
        if (!empty($params['minimum_tenure'])) {
            $targetRoles = $params['allRoles'] ?? [];
            $minYears = $params['minimum_tenure'];
            $excluded = [];
            $scoredCandidates = $scoredCandidates->filter(function($candidate) use ($targetRoles, $minYears, &$excluded) {
                $total = $this->calculateTotalRoleExperience($candidate->id, $targetRoles);
                if ($total < $minYears) {
                    $excluded[] = [
                        'id' => $candidate->id,
                        'name' => $candidate->forename . ' ' . $candidate->surname,
                        'total_role_experience' => $total
                    ];
                    return false;
                }
                return true;
            })->values();
            Log::info("📊 Candidates after minimum role experience filter: " . $scoredCandidates->count());
            if (!empty($excluded)) {
                Log::info("🚫 Excluded candidates due to insufficient cumulative experience:", $excluded);
            }
        }

        // Skip quality filtering - show all scored candidates to users
        // Scoring is still calculated for display purposes
        Log::info("📊 Candidates after scoring (no filtering): " . $scoredCandidates->count());
        
        // Convert to array to ensure we're working in-memory only
        $candidatesArray = $scoredCandidates->toArray();
        
        // Sort by target company priority first, then by total score (descending order)
        usort($candidatesArray, function($a, $b) use ($params) {
            // Calculate company priority scores
            $aCompanyScore = $this->calculateCompanyPreferenceScore($a, $params['companies'] ?? []);
            $bCompanyScore = $this->calculateCompanyPreferenceScore($b, $params['companies'] ?? []);
            
            // First sort by company preference (higher is better)
            if ($aCompanyScore !== $bCompanyScore) {
                return $bCompanyScore <=> $aCompanyScore;
            }
            
            // Then sort by total score (higher is better)
            $aTotalScore = $a->total_score ?? 0;
            $bTotalScore = $b->total_score ?? 0;
            
            if ($aTotalScore !== $bTotalScore) {
                return $bTotalScore <=> $aTotalScore; // Descending order
            }
            
            // Finally, use ID as tiebreaker for deterministic ordering
            $aId = is_array($a) ? ($a['id'] ?? 0) : ($a->id ?? 0);
            $bId = is_array($b) ? ($b['id'] ?? 0) : ($b->id ?? 0);
            
            return $aId <=> $bId; // Ascending order by ID for consistency
        });
        
        // No limit - show all candidates
        $scoredCandidates = collect($candidatesArray);
        Log::info("📊 Final candidates (no limit): " . count($candidatesArray));
        
        // Prepare data for pipeline insertion
        $pipelineData = $this->preparePipelineData($scoredCandidates, $planData, $user);
        
        // Insert into pipeline if candidates found
        if (!empty($pipelineData)) {
            $this->insertCandidatesIntoPipeline($pipelineData);
            $this->createSearchNotification($user->id);
        }
        
        // Log final candidate distribution
        Log::info("📊 Final candidates distributed by total score");
        
        Log::info("📊 Search completed: " . count($pipelineData) . " candidates added to pipeline");
        
        return [
            'candidates' => $pipelineData,
            'count' => count($pipelineData)
        ];
    }

    /**
     * Calculate total experience in any of the target roles (across all career history)
     * @param int $candidateId
     * @param array $targetRoles
     * @return float Years of experience
     */
    protected function calculateTotalRoleExperience($candidateId, $targetRoles)
    {
        // Use CareerHistories model
        $histories = \App\Models\CareerHistories::where('people_id', $candidateId)->get();
        $total = 0.0;
        foreach ($histories as $history) {
            foreach ($targetRoles as $role) {
                $escaped = preg_quote($role, '/');
                // Use same logic as addRoleMatching: match as whole word/phrase
                if (preg_match('/\\b' . $escaped . '\\b/i', $history->role)) {
                    $total += $history->tenure ?? 0;
                    break;
                }
            }
        }
        return $total;
    }

    /**
     * Normalize search parameters from plan data
     * 
     * @param array $planData The plan data
     * @return array Normalized search parameters
     */
    protected function normalizeSearchParams($planData)
    {
        return [
            'gender' => $planData['gender'] ?? null,
            'roles' => $this->normalizeArray($planData['target_roles'] ?? []),
            'stepUpRoles' => $this->normalizeArray($planData['step_up_candidates'] ?? []),
            'allRoles' => array_unique(array_merge(
                $this->normalizeArray($planData['target_roles'] ?? []),
                $this->normalizeArray($planData['acronyms'] ?? []),
                $this->normalizeArray($planData['step_up_candidates'] ?? [])
            )),
            'qualifications' => $this->normalizeArray($planData['qualifications'] ?? []),
            'skills' => $this->normalizeArray($planData['skills'] ?? []),
            'countries' => $this->normalizeArray($planData['country'] ?? []),
            'companies' => $this->normalizeArray($planData['companies'] ?? []),
            'include_alumni' => $planData['include_alumni'] ?? null, // No default - must be explicitly set
            'minimum_tenure' => $planData['minimum_tenure'] ?? null, // Add minimum tenure
        ];
    }
    
    /**
     * Normalize array input - handle strings, arrays, and filter out 'none'
     */
    protected function normalizeArray($input)
    {
        if (empty($input) || $input === ['none']) {
            return [];
        }
        
        $array = is_array($input) ? $input : [$input];
        return array_filter(array_map('trim', $array), fn($item) => !empty($item) && $item !== 'none');
    }
    
    /**
     * Find external candidates from People table
     */
    protected function findExternalCandidates($params, $user)
    {
        if (empty($params['allRoles'])) {
            return collect([]);
        }
        
        Log::info("🔎 EXTERNAL CANDIDATES SEARCH: Building query", [
            'include_alumni' => $params['include_alumni'] ?? 'not set',
            'searching_companies' => $params['companies'] ?? [],
            'searching_roles' => $params['allRoles'] ?? [],
            'minimum_tenure' => $params['minimum_tenure'] ?? 'not specified'
        ]);
        
        $query = People::query()
            ->whereNotIn('status', ['Submitted', 'Reported'])
            ->where('company_id', '!=', $user->company_id)
            ->where(function($query) use ($params) {
                // Must match target roles
                $this->addRoleMatching($query, $params['allRoles']);
            })
            ->where(function($query) use ($params) {
                // MUST have target company experience (current OR previous based on preference)
                if (!empty($params['companies'])) {
                    $includeAlumni = $params['include_alumni'];
                    
                    // If include_alumni is not set, we should not proceed (this should never happen in normal flow)
                    if ($includeAlumni === null) {
                        Log::warning('include_alumni preference not set for plan search', ['plan_id' => $params['plan_id'] ?? 'unknown']);
                        $query->whereRaw('1 = 0'); // Return no results if preference not set
                        return;
                    }
                    
                    Log::info("🏢 COMPANY SEARCH LOGIC:", [
                        'include_alumni' => $includeAlumni,
                        'search_mode' => $includeAlumni ? 'Current + Alumni' : 'Current Only',
                        'target_companies' => $params['companies']
                    ]);
                    
                    if ($includeAlumni === true) {
                        // Include BOTH current employees AND alumni
                        $query->where(function($subQuery) use ($params) {
                            // Current employees at target companies
                            $subQuery->where(function($currentQuery) use ($params) {
                                foreach($params['companies'] as $company) {
                                    $this->addCompanyMatching($currentQuery, $company);
                                }
                            });
                            
                            // OR Alumni who worked at target companies in the last 5 years
                            $this->addAlumniSearch($subQuery, $params['companies']);
                        });
                        Log::info("✅ Including current employees and alumni who left target companies within last 5 years");
                    } else {
                        // Only current employees
                        $query->where(function($currentQuery) use ($params) {
                            foreach($params['companies'] as $company) {
                                $this->addCompanyMatching($currentQuery, $company);
                            }
                        });
                        Log::info("❌ Excluding all alumni (current employees only)");
                    }
                } else {
                    // If no companies specified, return no results
                    $query->whereRaw('1 = 0');
                }
            })
            ->when(!empty($params['gender']) && $params['gender'] !== 'Not required', 
                fn($q) => $q->where('gender', $params['gender']))
            ->when(!empty($params['countries']), 
                fn($q) => $q->whereIn('country', $params['countries']))
            // REMOVED SQL-level tenure filter; cumulative experience is now checked in PHP
            ->orderBy('id'); // Add deterministic ordering
            
        // Log the SQL query before execution
        Log::info("📝 EXTERNAL CANDIDATES SQL QUERY:", [
            'sql' => $query->toSql(),
            'bindings' => $query->getBindings()
        ]);
        
        $results = $query->get()
            ->map(function($candidate) use ($params) {
                $candidate->role_score = $this->calculateRoleScore($candidate->latest_role, $params);
                $candidate->type = 'External-System';
                return $candidate;
            });
            
        return $results;
    }
    
    /**
     * Find internal candidates from InternalPeople table
     */
    protected function findInternalCandidates($params, $user)
    {
        if (empty($params['allRoles'])) {
            return collect([]);
        }
        
        Log::info("🔎 INTERNAL CANDIDATES SEARCH: Building query", [
            'include_alumni' => $params['include_alumni'] ?? 'not set',
            'searching_companies' => $params['companies'] ?? [],
            'searching_roles' => $params['allRoles'] ?? [],
            'minimum_tenure' => $params['minimum_tenure'] ?? 'not specified'
        ]);
        
        $query = InternalPeople::query()
            ->where('company_id', $user->company_id)
            ->where('forename', '!=', 'Vacant')
            ->where(function($query) use ($params) {
                // Note: InternalPeople uses 'latest_role' field like People table
                // Must match target roles
                $this->addRoleMatching($query, $params['allRoles']);
            })
            ->where(function($query) use ($params) {
                // MUST have target company experience (current OR previous based on preference)
                if (!empty($params['companies'])) {
                    $includeAlumni = $params['include_alumni'];
                    
                    // If include_alumni is not set, we should not proceed (this should never happen in normal flow)
                    if ($includeAlumni === null) {
                        Log::warning('include_alumni preference not set for plan search', ['plan_id' => $params['plan_id'] ?? 'unknown']);
                        $query->whereRaw('1 = 0'); // Return no results if preference not set
                        return;
                    }
                    
                    Log::info("🏢 COMPANY SEARCH LOGIC:", [
                        'include_alumni' => $includeAlumni,
                        'search_mode' => $includeAlumni ? 'Current + Alumni' : 'Current Only',
                        'target_companies' => $params['companies']
                    ]);
                    
                    if ($includeAlumni === true) {
                        // Include BOTH current employees AND alumni
                        $query->where(function($subQuery) use ($params) {
                            // Current employees at target companies
                            $subQuery->where(function($currentQuery) use ($params) {
                                foreach($params['companies'] as $company) {
                                    $this->addCompanyMatching($currentQuery, $company);
                                }
                            });
                            
                            // OR Alumni who worked at target companies in the last 5 years
                            $this->addAlumniSearchForInternal($subQuery, $params['companies']);
                        });
                        Log::info("✅ Including current employees and alumni who left target companies within last 5 years");
                    } else {
                        // Only current employees
                        $query->where(function($currentQuery) use ($params) {
                            foreach($params['companies'] as $company) {
                                $this->addCompanyMatching($currentQuery, $company);
                            }
                        });
                        Log::info("❌ Excluding all alumni (current employees only)");
                    }
                } else {
                    // If no companies specified, return no results
                    $query->whereRaw('1 = 0');
                }
            })
            ->when(!empty($params['gender']), 
                fn($q) => $q->where('gender', $params['gender']))
            ->when(!empty($params['countries']), function($query) use ($params) {
                try {
                    $query->whereIn('country', $params['countries']);
                } catch (\Exception $e) {
                    // Fallback to location if country column doesn't exist
                    $query->whereIn('location', $params['countries']);
                }
            })
            // REMOVED SQL-level tenure filter; cumulative experience is now checked in PHP
            ->orderBy('id'); // Add deterministic ordering
            
        // Log the SQL query before execution
        Log::info("📝 INTERNAL CANDIDATES SQL QUERY:", [
            'sql' => $query->toSql(),
            'bindings' => $query->getBindings()
        ]);
        
        $results = $query->get()
            ->map(function($candidate) use ($params) {
                $candidate->role_score = $this->calculateRoleScore($candidate->latest_role, $params);
                $candidate->type = 'Internal-System';
                return $candidate;
            });
            
        return $results;
    }
    
    /**
     * Add role matching to query with abbreviation expansion
     */
    protected function addRoleMatching($query, $roles)
    {
        // First, collect all unique role variations to avoid duplicates
        $allVariations = [];
        
        foreach($roles as $role) {
            // Get all variations of the role (including abbreviations)
            $roleVariations = $this->expandRoleAbbreviations($role);
            
            foreach($roleVariations as $variation) {
                // Use lowercase for deduplication
                $allVariations[strtolower($variation)] = $variation;
            }
        }
        
        // Now add the unique variations to the query - STRICT matching using REGEXP on latest_role only
        foreach($allVariations as $variation) {
            // Escape regex special chars except space
            $escaped = preg_quote($variation, '/');
            // For abbreviations (e.g. CRO), match as a whole word
            if (preg_match('/^[A-Z]{2,}$/', $variation)) {
                $regex = "\\b$escaped\\b";
            } else {
                // For multi-word roles (e.g. Chief Risk), match as phrase, possibly followed by more words
                // (Chief Risk Officer, Chief Risk and Compliance Officer, etc.)
                $regex = "\\b$escaped( [A-Za-z]+)*\\b";
            }
            // Use REGEXP for strict matching
            $query->orWhere('latest_role', 'REGEXP', $regex);
        }
    }
    
    /**
     * Normalize and return the role as a single-element array.
     * This function no longer expands abbreviations for specific roles, treating all roles equally.
     */
    protected function expandRoleAbbreviations($role)
    {
        // Normalize role for lookup (trim whitespace, case-insensitive)
        $normalizedRole = trim($role);
        return [$normalizedRole];
    }
    
    /**
     * Add alumni search with 5-year recency filter
     */
    protected function addAlumniSearch($query, $companies)
    {
        $fiveYearsAgo = now()->subYears(5)->format('Y-m-d');
        
        $query->orWhereExists(function($query) use ($companies, $fiveYearsAgo) {
            $query->select(DB::raw(1))
                ->from('career_histories')
                ->whereColumn('career_histories.people_id', 'people.id')
                ->where(function($careerQuery) use ($companies, $fiveYearsAgo) {
                    // Must have worked at one of the target companies
                    $careerQuery->whereIn('past_company_id', function($companyQuery) use ($companies) {
                        $companyQuery->select('id')
                            ->from('companies')
                            ->where(function($nameQuery) use ($companies) {
                                foreach($companies as $company) {
                                    $nameQuery->orWhere('name', 'LIKE', "%$company%");
                                }
                            });
                    });
                    // And left within the last 5 years (or still there)
                    $careerQuery->where(function($dateQuery) use ($fiveYearsAgo) {
                        $dateQuery->whereNull('end_date') // Still working there
                                  ->orWhere('end_date', '>=', $fiveYearsAgo); // Left within 5 years
                    });
                });
        });
    }
    
    /**
     * Add alumni search with 5-year recency filter for internal people
     */
    protected function addAlumniSearchForInternal($query, $companies)
    {
        $fiveYearsAgo = now()->subYears(5)->format('Y-m-d');
        
        $query->orWhereExists(function($query) use ($companies, $fiveYearsAgo) {
            $query->select(DB::raw(1))
                ->from('internal_career_histories')
                ->whereColumn('internal_career_histories.people_id', 'internal_people.id')
                ->where(function($careerQuery) use ($companies, $fiveYearsAgo) {
                    // Must have worked at one of the target companies
                    $careerQuery->whereIn('past_company_id', function($companyQuery) use ($companies) {
                        $companyQuery->select('id')
                            ->from('companies')
                            ->where(function($nameQuery) use ($companies) {
                                foreach($companies as $company) {
                                    $nameQuery->orWhere('name', 'LIKE', "%$company%");
                                }
                            });
                    });
                    // And left within the last 5 years (or still there)
                    $careerQuery->where(function($dateQuery) use ($fiveYearsAgo) {
                        $dateQuery->whereNull('end_date') // Still working there
                                  ->orWhere('end_date', '>=', $fiveYearsAgo); // Left within 5 years
                    });
                });
        });
    }
    
    /**
     * Add flexible company matching to handle variations like "Legal and Group" vs "Legal & Group"
     */
    protected function addCompanyMatching($query, $company)
    {
        // Original company name
        $query->orWhere('company_name', 'LIKE', "%$company%");
        
        // Handle "and" vs "&" variations
        if (stripos($company, ' and ') !== false) {
            $ampersandVersion = str_ireplace(' and ', ' & ', $company);
            $query->orWhere('company_name', 'LIKE', "%$ampersandVersion%");
        } elseif (stripos($company, ' & ') !== false) {
            $andVersion = str_ireplace(' & ', ' and ', $company);
            $query->orWhere('company_name', 'LIKE', "%$andVersion%");
        }
        
        // Handle "Group" vs "Grp" variations
        if (stripos($company, 'Group') !== false) {
            $grpVersion = str_ireplace('Group', 'Grp', $company);
            $query->orWhere('company_name', 'LIKE', "%$grpVersion%");
        } elseif (stripos($company, 'Grp') !== false) {
            $groupVersion = str_ireplace('Grp', 'Group', $company);
            $query->orWhere('company_name', 'LIKE', "%$groupVersion%");
        }
        
        // Handle "Limited" vs "Ltd" variations
        if (stripos($company, 'Limited') !== false) {
            $ltdVersion = str_ireplace('Limited', 'Ltd', $company);
            $query->orWhere('company_name', 'LIKE', "%$ltdVersion%");
        } elseif (stripos($company, 'Ltd') !== false) {
            $limitedVersion = str_ireplace('Ltd', 'Limited', $company);
            $query->orWhere('company_name', 'LIKE', "%$limitedVersion%");
        }
        
        // Handle "Corporation" vs "Corp" variations
        if (stripos($company, 'Corporation') !== false) {
            $corpVersion = str_ireplace('Corporation', 'Corp', $company);
            $query->orWhere('company_name', 'LIKE', "%$corpVersion%");
        } elseif (stripos($company, 'Corp') !== false) {
            $corporationVersion = str_ireplace('Corp', 'Corporation', $company);
            $query->orWhere('company_name', 'LIKE', "%$corporationVersion%");
        }
    }
    
    /**
     * Calculate role match score
     */
    protected function calculateRoleScore($candidateRole, $params)
    {
        // Direct role match
        foreach($params['roles'] as $role) {
            if (stripos($candidateRole, $role) !== false) {
                return 1.0;
            }
        }
        
        // Step-up role match
        foreach($params['stepUpRoles'] as $role) {
            if (stripos($candidateRole, $role) !== false) {
                return 0.75;
            }
        }
        
        // Default for other matches
        return 0.6;
    }
    
    /**
     * Score all candidates with consolidated logic
     */
    protected function scoreAllCandidates($candidates, $params, $planData)
    {
        return $candidates->map(function($candidate) use ($params, $planData) {
            // Skills matching
            $candidate->skills_match = $this->calculateSkillsScore($candidate, $params['skills']);
            
            // Education matching  
            $candidate->education_match = $this->calculateEducationScore($candidate, $params['qualifications']);
            
            // Location matching
            $candidate->location_match = 1.0; // Default
            
            // Gender matching
            $candidate->gender_match = (!empty($params['gender']) && $params['gender'] !== 'Not required') 
                ? ($candidate->gender === $params['gender'] ? 1.0 : 0.0) 
                : 1.0;
            
            // Tenure matching
            $candidate->tenure_match = $this->calculateTenureScore($candidate, $planData['minimum_tenure'] ?? null);
            
            // Calculate total score
            $candidate->total_score = $candidate->role_score + 
                                    $candidate->skills_match + 
                                    $candidate->education_match + 
                                    $candidate->location_match + 
                                    $candidate->tenure_match;
            
            return $candidate;
        });
    }
    
    /**
     * Calculate skills match score
     */
    protected function calculateSkillsScore($candidate, $requiredSkills)
    {
        if (empty($requiredSkills)) {
            return 0.0;
        }
        
        $candidateSkills = strtolower($candidate->skills ?? '');
        $matchCount = 0;
        
        foreach($requiredSkills as $skill) {
            if (stripos($candidateSkills, strtolower($skill)) !== false) {
                $matchCount++;
            }
        }
        
        return count($requiredSkills) > 0 ? $matchCount / count($requiredSkills) : 0.0;
    }
    
    /**
     * Calculate education match score
     */
    protected function calculateEducationScore($candidate, $requiredEducation)
    {
        if (empty($requiredEducation)) {
            return 0.0;
        }
        
        $candidateEducation = strtolower($candidate->educational_history ?? '');
        $matchCount = 0;
        
        foreach($requiredEducation as $education) {
            if (stripos($candidateEducation, strtolower($education)) !== false) {
                $matchCount++;
            }
        }
        
        return count($requiredEducation) > 0 ? $matchCount / count($requiredEducation) : 0.0;
    }
    
    /**
     * Calculate tenure match score
     */
    protected function calculateTenureScore($candidate, $minimumTenure)
    {
        if (empty($minimumTenure)) {
            return 1.0;
        }
        
        $candidateTenure = $candidate->tenure ?? 0;
        
        // If candidate meets or exceeds minimum tenure
        if ($candidateTenure >= $minimumTenure) {
            return 1.0; // Full score for meeting requirement
        }
        
        // Penalize candidates below minimum tenure
        // No partial credit - either they meet the requirement or they don't
        return 0.0;
    }
    
    /**
     * Calculate company preference score - prioritizes current over previous employees
     */
    protected function calculateCompanyPreferenceScore($candidate, $targetCompanies)
    {
        if (empty($targetCompanies)) {
            return 0.0;
        }
        
        $currentCompany = strtolower($candidate->company_name ?? '');
        $summary = strtolower($candidate->summary ?? '');
        
        // Check if currently works at target company (highest priority)
        foreach($targetCompanies as $company) {
            if (stripos($currentCompany, strtolower($company)) !== false) {
                return 2.0; // High bonus for current employees
            }
        }
        
        // Check if summary mentions target company (indicates experience)
        foreach($targetCompanies as $company) {
            if (stripos($summary, strtolower($company)) !== false) {
                return 1.0; // Bonus for company experience mentioned in summary
            }
        }
        
        return 0.0; // No target company experience
    }
    
    // DEPRECATED METHODS - Kept for compatibility, will be removed in next version
    
    protected function findExternalSystemCandidates($proles, $psteps, $planData, $user, $gen)
    {
        // Redirect to new simplified method
        $params = [
            'allRoles' => array_merge($proles ?? [], $psteps ?? []),
            'roles' => $proles ?? [],
            'stepUpRoles' => $psteps ?? [],
            'gender' => $gen,
            'countries' => $planData['country'] ?? [],
            'companies' => $planData['companies'] ?? []
        ];
        
        $candidates = $this->findExternalCandidates($params, $user);
        
        return [
            'filteredPeople' => $candidates,
            'filteredPeopleidslvl1' => $candidates->pluck('id'),
            'sfilteredPeople' => collect([]),
            'sfilteredPeopleidslvl1' => collect([])
        ];
    }
    
    /**
     * Find candidates from the internal people table (InternalPeople)
     */
    protected function findInternalSystemCandidates($proles, $psteps, $planData, $user, $gen, $scountry)
    {
        $infilteredPeople = InternalPeople::query()
            ->where('company_id', $user->company_id)
            ->where('forename', '!=', "Vacant")
            ->when(!empty($proles) || !empty($psteps), function ($query) use ($proles, $psteps) {
                $roles = array_merge($proles, $psteps);
                
                $query->where(function ($subquery) use ($roles) {
                    foreach($roles as $role) {
                        $subquery->orWhere("latest_role", "LIKE", "%$role%");
                    }
                });
            })
            ->when(!empty($gen) || !empty($scountry), function ($query) use ($gen, $scountry) {
                if (!empty($gen)) {
                    $query->where('gender', $gen);
                }
                if (!empty($scountry)) {
                    // Check if using country column or fall back to location if needed
                    try {
                        $query->whereIn('country', $scountry);
                    } catch (\Exception $e) {
                        // If country column doesn't exist, use location instead
                        Log::warning("Country column not found, using location instead", ['error' => $e->getMessage()]);
                        $query->whereIn('location', $scountry);
                    }
                }
            })
            ->when(!empty($planData['companies']) && $planData['companies'] != ['none'], function ($query) use ($planData) {
                $query->where(function ($subquery) use ($planData) {
                    foreach ($planData['companies'] as $company) {
                        $subquery->orWhere('company_name', 'LIKE', "%{$company}%");
                    }
                });
            })
            ->get();

        // Assign role scores and types
        $infilteredPeople = $infilteredPeople->map(function ($item) use ($psteps) {
            $item['role_score'] = in_array($item->latest_role, $psteps) ? 0.75 : 1;
            $item['type'] = "Internal-System";
            return $item;
        });

        
        return $infilteredPeople;
    }
    
    // DEPRECATED: findCandidatesFromSummary - removed for simplification
    protected function findCandidatesFromSummary($planData, $user, $excludeIds)
    {
        // Summary search is now integrated into main role-based search
        // This method returns empty collection to maintain compatibility
        return collect([]);
    }
    
    /**
     * Build search terms from plan data requirements
     */
    protected function buildSummarySearchTerms($planData)
    {
        $terms = [];
        
        // Add target roles
        if (!empty($planData['target_roles'])) {
            $terms = array_merge($terms, $planData['target_roles']);
        }
        
        // Add alternative role titles
        if (!empty($planData['alternative_roles_titles'])) {
            $terms = array_merge($terms, $planData['alternative_roles_titles']);
        }
        
        // Add step-up candidates
        if (!empty($planData['step_up_candidates']) && $planData['step_up_candidates'] !== ['none']) {
            $terms = array_merge($terms, $planData['step_up_candidates']);
        }
        
        // Add skills
        if (!empty($planData['skills'])) {
            $terms = array_merge($terms, $planData['skills']);
        }
        
        // Add related terms based on role
        $targetRoles = $planData['target_roles'] ?? [];
        if (!is_array($targetRoles)) {
            $targetRoles = $targetRoles ? [$targetRoles] : [];
        }
        $relatedTerms = $this->getRelatedTerms($targetRoles);
        $terms = array_merge($terms, $relatedTerms);
        
        return array_unique($terms);
    }
    
    /**
     * Get related terms based on target roles
     */
    protected function getRelatedTerms($targetRoles)
    {
        // Return empty array - no role-specific enhancements
        // The search will rely purely on the provided plan data terms
        return [];
    }
    
    // DEPRECATED: combineCandidates - replaced by direct collection concat
    protected function combineCandidates($filteredPeople, $sfilteredPeople, $infilteredPeople, $summaryBasedPeople)
    {
        // Simplified version for compatibility
        return collect($filteredPeople)->concat($infilteredPeople);
    }
    
    // DEPRECATED: calculateCandidateScores - replaced by scoreAllCandidates
    protected function calculateCandidateScores($candidates, $pEdQual, $pskills, $skillCount, $planData)
    {
        // Calculate education match scores
        $educationCounts = People::query()
            ->whereIn('people.educational_history', $pEdQual)
            ->join('people as filtered_people', 'people.id', '=', 'filtered_people.id')
            ->groupBy('people.id')
            ->selectRaw('people.id, COUNT(filtered_people.id) as education_match_count')
            ->get();

        // Create a map of education match counts indexed by person ID
        $educationMatchCountMap = $educationCounts->pluck('education_match_count', 'id');

        // Add education match scores to candidates
        $candidatesWithEducation = $candidates->map(function ($person) use ($educationMatchCountMap) {
            $person->education_match_count = $educationMatchCountMap->get($person->id, 0);
            return $person;
        });

        // Calculate location match scores
        $candidatesWithLocation = $candidatesWithEducation->map(function ($person) {
            $person->location_match_count = 1; // Default to 1 for now
            return $person;
        });

        // Calculate skill match scores
        $skillsScoreCount = Skills::query()
            ->whereIn('skill_name', $pskills)
            ->groupBy('people_id')
            ->selectRaw('skills.people_id, COUNT(skills.people_id) / ? as skill_score', ['skillCount' => $skillCount])
            ->get();

        // Create a map of skill match scores indexed by person ID
        $skillsScoreMap = $skillsScoreCount->pluck('skill_score', 'people_id');

        // Add skill match scores to candidates
        $candidatesWithSkills = $candidatesWithLocation->map(function ($person) use ($skillsScoreMap) {
            $person->skill_score = $skillsScoreMap->get($person->id, 0);
            return $person;
        });

        // Calculate gender match scores
        $candidatesWithGender = $candidatesWithSkills->each(function ($person) use ($planData) {
            $person->gender_score = ($person->gender === $planData['gender']) ? 1 : 0;
        });

        // Calculate tenure match scores
        if ($planData['minimum_tenure'] !== null) {
            $candidatesWithTenure = $candidatesWithGender->map(function ($person) use ($planData) {
                $tenancyDifference = $person->tenure - $planData['minimum_tenure'];
                $tenancyDifferenceAbsolute = abs($tenancyDifference);

                if ($tenancyDifference < 0) {
                    if ($tenancyDifferenceAbsolute <= 2) {
                        $person->tenancy_score = 0;
                    } else {
                        $person->tenancy_score = 1;
                    }
                } else {
                    $person->tenancy_score = 1;
                }
                return $person;
            });
        } else {
            $candidatesWithTenure = $candidatesWithGender->map(function ($person) {
                $person->tenancy_score = 1;
                return $person;
            });
        }
        
        return $candidatesWithTenure;
    }
    
    /**
     * Check if a candidate is a duplicate in the pipeline for rerun deduplication
     */
    protected function isDuplicateCandidate($candidate, $planId, $getValue)
    {
        $linkedInUrl = $getValue('linkedinURL');
        $firstName = $getValue('forename');
        $lastName = $getValue('surname');
        $companyName = $getValue('company_name');
        
        // First check by LinkedIn URL if available
        if (!empty($linkedInUrl)) {
            $existingByUrl = pipeline::where('plan_id', $planId)
                ->where('linkedinURL', $linkedInUrl)
                ->exists();
                
            if ($existingByUrl) {
                Log::info("RERUN DEDUPE: Duplicate found by LinkedIn URL", [
                    'name' => "$firstName $lastName",
                    'linkedinURL' => $linkedInUrl,
                    'plan_id' => $planId
                ]);
                return true;
            }
        }
        
        // If no LinkedIn URL or no match, check by name + company fuzzy match
        if (!empty($firstName) && !empty($lastName) && !empty($companyName)) {
            $existingByNameCompany = pipeline::where('plan_id', $planId)
                ->where('first_name', 'like', "%$firstName%")
                ->where('last_name', 'like', "%$lastName%")
                ->where('company_name', 'like', "%$companyName%")
                ->exists();
                
            if ($existingByNameCompany) {
                Log::info("RERUN DEDUPE: Duplicate found by name + company fuzzy match", [
                    'name' => "$firstName $lastName",
                    'company' => $companyName,
                    'plan_id' => $planId
                ]);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Format candidate data for pipeline insertion with rerun deduplication
     */
    protected function preparePipelineData($scoredCandidates, $planData, $user) 
    {
        $isRerun = $planData['is_rerun'] ?? false;
        $planId = $planData['plan_id'];
        $filteredCandidates = [];
        $duplicateCount = 0;
        
        foreach ($scoredCandidates as $person) {
            // Handle both array and object format
            $getValue = function($key, $default = null) use ($person) {
                if (is_array($person)) {
                    return $person[$key] ?? $default;
                }
                return $person->$key ?? $default;
            };
            
            // Skip duplicates during rerun
            if ($isRerun && $this->isDuplicateCandidate($person, $planId, $getValue)) {
                $duplicateCount++;
                continue;
            }
            
            $filteredCandidates[] = [
                'plan_id'            => $planId,
                'user_id'            => $user->id,
                'people_id'          => $getValue('id'),
                'first_name'         => $getValue('forename'),
                'last_name'          => $getValue('surname'),
                'middle_name'        => $getValue('middle_name'),
                'other_name'         => $getValue('other_name'),
                'gender'             => $getValue('gender'),
                'diverse'            => $getValue('diverse'),
                'location'           => $getValue('location', ''),
                'country'            => $getValue('country', ''),
                'city'               => $getValue('city'),
                'summary'            => $getValue('summary'),
                'linkedinURL'        => $getValue('linkedinURL'),
                'latest_role'        => $getValue('latest_role'),
                'company_id'         => $getValue('company_id'),
                'company_name'       => $getValue('company_name'),
                'start_date'         => $getValue('start_date'),
                'end_date'           => $getValue('end_date'),
                'tenure'             => $getValue('tenure'),
                'function'           => $getValue('function'),
                'division'           => $getValue('division'),
                'seniority'          => $getValue('seniority'),
                'exco'               => $getValue('exco'),
                'career_history'     => $getValue('career_history'),
                'educational_history' => $getValue('educational_history'),
                'skills'             => $getValue('skills'),
                'languages'          => $getValue('languages'),
                'skills_match'       => $getValue('skills_match', 0),
                'education_match'    => $getValue('education_match', 0),
                'location_match'     => $getValue('location_match', 1),
                'role_match'         => $getValue('role_score', 0),
                'readiness'          => $getValue('readiness'),
                'other_tags'         => $getValue('other_tags'),
                'gender_match'       => $getValue('gender_match', 1),
                'tenure_match'       => $getValue('tenure_match', 1),
                'total_score'        => $getValue('total_score', 0),
                'people_type'        => $getValue('type'),
            ];
        }
        
        if ($isRerun && $duplicateCount > 0) {
            Log::info("RERUN DEDUPE: Skipped $duplicateCount duplicate candidates for plan $planId during rerun");
        }
        
        return $filteredCandidates;
    }
    
    /**
     * Insert candidate data into the pipeline table
     */
    protected function insertCandidatesIntoPipeline($pipelineData)
    {
        if (!empty($pipelineData)) {
            // Break the data into smaller chunks to avoid max_allowed_packet limit
            foreach (array_chunk($pipelineData, 50) as $chunk) {
                pipeline::insert($chunk);
            }
        }
    }
    
    /**
     * Create notification for completed search
     */
    protected function createSearchNotification($userId)
    {
        $notificationData = [
            'user_id' => $userId,
            'created_at' => now(),
        ];
        
        DB::table('job_queues_notification')->insert($notificationData);
    }
}