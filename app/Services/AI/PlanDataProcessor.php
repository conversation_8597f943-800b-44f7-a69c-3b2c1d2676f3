<?php

namespace App\Services\AI;

use App\Models\Account;
use App\Models\Company;
use App\Models\People;
use App\Models\SuccessionPlan;
use App\Models\SuccessRequirements;
use App\Models\notifications;
use Illuminate\Support\Facades\Log;

class PlanDataProcessor
{
    /**
     * Process and store a succession plan based on AI-generated data
     * 
     * @param array $planData The plan data to process
     * @param object $user The current user
     * @return array Result with status and processed plan data
     */
    public function processPlan($planData, $user)
    {
        Log::info("🔍 VALIDATION: Starting plan data processing", [
            'plan_name' => $planData['plan_name'] ?? 'Unknown',
            'user_id' => $user->id,
            'has_target_roles' => !empty($planData['target_roles']),
            'has_companies' => !empty($planData['companies']),
            'has_skills' => !empty($planData['skills'])
        ]);
        
        try {
            // Add company interest data to plan if available
            $enrichedPlanData = $this->enrichPlanData($planData, $user);
            
            // Create the plan record in the database
            $plan = $this->createPlanRecord($enrichedPlanData, $user);
            
            // Add plan ID to the plan data
            $enrichedPlanData['plan_id'] = $plan->id;
            
            // Create success requirements records
            $this->createSuccessRequirements($enrichedPlanData, $plan);
            
            // Create notification
            $this->createPlanNotification($enrichedPlanData, $plan, $user);
            
            // Validate data persistence
            Log::info("✅ VALIDATION: Plan data successfully persisted", [
                'plan_id' => $plan->id,
                'plan_exists_in_db' => SuccessionPlan::find($plan->id) !== null,
                'requirements_count' => SuccessRequirements::where('plan_id', $plan->id)->count(),
                'notification_created' => notifications::where('plan_id', $plan->id)->exists()
            ]);
            
            return [
                'success' => true, 
                'plan' => $plan,
                'plan_data' => $enrichedPlanData
            ];
            
        } catch (\Exception $e) {
            // Check if this is a max_allowed_packet error
            if (strpos($e->getMessage(), 'max_allowed_packet') !== false || strpos($e->getMessage(), '1153') !== false) {
                Log::error("PLAN PROCESSING: max_allowed_packet error - plan data too large", [
                    'error' => $e->getMessage(),
                    'plan_name' => $planData['plan_name'] ?? 'Unknown',
                    'suggested_solution' => 'Reduce plan data size or increase MySQL max_allowed_packet setting'
                ]);
                
                return [
                    'success' => false,
                    'error' => 'Plan data is too large for database. Please reduce the number of companies, skills, or other plan criteria.'
                ];
            }
            
            Log::error("PLAN PROCESSING: Failed to process plan", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Enrich plan data with account-specific company interests
     * 
     * @param array $planData The original plan data
     * @param object $user The current user
     * @return array Enriched plan data
     */
    protected function enrichPlanData($planData, $user)
    {
        $interestedCompanies = false;
        $interestedIndustries = false;
        $interestedSectors = false;
        
        // Get user's account information
        $accountObj = Account::where('id', $user->account_id)->first();
        
        if ($accountObj) {
            // Extract company interests
            if ($accountObj->company_of_interest && $accountObj->company_of_interest != "") {
                $interestedCompanies = explode(",", $accountObj->company_of_interest);
            }
            else if ($accountObj->industry_interest && $accountObj->industry_interest != "") {
                $interestedIndustries = explode(",", $accountObj->industry_interest);
            }
            else if ($accountObj->sector_interest && $accountObj->sector_interest != "") {
                $interestedSectors = explode(",", $accountObj->sector_interest);
            }
        }
        
        // Determine company IDs based on interest
        $companyIds = [];
        if ($interestedCompanies && count($interestedCompanies) > 0) {
            $companyIds = $interestedCompanies;
        }
        else if ($interestedIndustries && count($interestedIndustries) > 0) {
            $companyIds = Company::whereIn('industry', $interestedIndustries)->pluck('id')->toArray();
        }
        else if ($interestedSectors && count($interestedSectors) > 0) {
            $companyIds = Company::whereIn('sector', $interestedSectors)->pluck('id')->toArray();
        }
        
        // Get company information - limit to prevent max_allowed_packet issues
        $companies = Company::select(['id', 'name', 'industry', 'sector'])
            ->where('id', '!=', $user->company_id)
            ->when(!empty($companyIds), function ($query) use ($companyIds) {
                $query->whereIn('id', $companyIds);
            })
            ->limit(1000) // Limit to prevent massive JSON payloads
            ->get()->map(function ($company) {
                return [
                    'value' => $company->name,
                    'label' => $company->name,
                    'industry' => $company->industry,
                    'sector' => $company->sector
                ];
            })->toArray();
        
        // Add company information to plan data
        $planData['available_companies'] = $companies;
        
        return $planData;
    }
    
    /**
     * Create the plan record in the database
     * 
     * @param array $planData The plan data
     * @param object $user The current user
     * @return SuccessionPlan The created plan
     */
    protected function createPlanRecord($planData, $user)
    {
        Log::info("📝 PLAN CREATION: Creating plan record in database", [
            'plan_name' => $planData['plan_name'],
            'user_id' => $user->id,
            'user_name' => $user->name,
            'timestamp' => now()->toDateTimeString()
        ]);
        
        // Create the plan record with all fields
        $plan = SuccessionPlan::create([
            'name'               => $planData['plan_name'],
            'plan_name'          => $planData['plan_name'],
            'description'        => $planData['description'],
            'minimum_Experience' => $planData['minimum_tenure'] ?? 0, // Default to 0 if missing
            'minimum_tenure'     => $planData['minimum_tenure'] ?? 0,
            'step_up'            => "1", // Default to enabled
            'ethnicity'          => $planData['is_ethnicity_important'] ?? 0,
            'age'                => 0, // Default to 0
            'status'             => "Draft",
            'candidate_status'   => "No Changes",
            'user_id'            => $user->id,
            'shared_with'        => "[]", // Default to empty JSON array
            // New fields
            'target_roles'       => json_encode($planData['target_roles'] ?? []),
            'companies'          => json_encode($planData['companies'] ?? []),
            'country'            => json_encode($planData['country'] ?? []),
            'gender'             => $planData['gender'] ?? 'Not required',
            'include_alumni'     => $planData['include_alumni'] ?? false,
            'skills'             => json_encode($planData['skills'] ?? []),
            'qualifications'     => json_encode($planData['qualifications'] ?? []),
            'step_up_candidates' => json_encode($planData['step_up_candidates'] ?? []),
            'alternative_roles_titles' => json_encode($planData['alternative_roles_titles'] ?? []),
            'acronyms'           => json_encode($planData['acronyms'] ?? []),
            'plan_data'          => json_encode($this->sanitizePlanDataForStorage($planData)) // Store essential plan data only
        ]);
        
        Log::info("✅ PLAN CREATION: Plan successfully created in database", [
            'plan_id' => $plan->id,
            'plan_name' => $plan->name,
            'table' => 'succession_plans',
            'created_at' => $plan->created_at
        ]);
        
        return $plan;
    }
    
    /**
     * Create success requirements records for the plan
     * 
     * @param array $planData The plan data
     * @param SuccessionPlan $plan The created plan
     * @return void
     */
    protected function createSuccessRequirements($planData, $plan)
    {
        $successRequirementsData = [];
        
        // Add target roles
        $target_roles = $planData['target_roles'];
        foreach ($target_roles as $role) {
            $successRequirementsData[] = [
                'plan_id' => $plan->id,
                'name'    => trim($role),
                'type'    => 'Role',
            ];
        }
        
        // Add step-up candidates if specified
        if ($planData['step_up_candidates'] != ['none']) {
            $step_up_candidates = $planData['step_up_candidates'];
            foreach ($step_up_candidates as $stepUpCandidate) {
                $successRequirementsData[] = [
                    'plan_id' => $plan->id,
                    'name'    => trim($stepUpCandidate),
                    'type'    => 'step_up',
                ];
            }
        }
        
        // Add tenure requirement if specified
        $minimum_tenure = $planData['minimum_tenure'] ?? null;
        if ($minimum_tenure) {
            $successRequirementsData[] = [
                'plan_id' => $plan->id,
                'name'    => $minimum_tenure,
                'type'    => 'Minimum_Tenure',
            ];
        }
        
        // Add location requirements if specified
        if ($planData['country'] != ['none']) {
            $countries = $planData['country'];
            foreach ($countries as $country) {
                $successRequirementsData[] = [
                    'plan_id' => $plan->id,
                    'name'    => $country,
                    'type'    => 'location',
                ];
            }
        }
        
        // Add professional skills
        $skills = $planData['skills'];
        foreach ($skills as $skill) {
            $successRequirementsData[] = [
                'plan_id' => $plan->id,
                'name'    => trim($skill),
                'type'    => 'professional_skill',
            ];
        }
        
        // Add gender requirement if specified
        if ($planData['gender'] != "Not Required") {
            $successRequirementsData[] = [
                'plan_id' => $plan->id,
                'name'    => trim($planData['gender']),
                'type'    => 'Gender',
            ];
        }
        
        // Add company requirements if specified
        if ($planData['companies'] != ['none']) {
            $companiesArr = $planData['companies'];
            foreach ($companiesArr as $cr) {
                $successRequirementsData[] = [
                    'plan_id' => $plan->id,
                    'name'    => trim($cr),
                    'type'    => 'Company',
                ];
            }
        }
        
        // Add alumni preference if specified
        if (isset($planData['include_alumni'])) {
            $successRequirementsData[] = [
                'plan_id' => $plan->id,
                'name'    => $planData['include_alumni'] ? 'Yes' : 'No',
                'type'    => 'include_alumni',
            ];
        }
        
        // Batch insert all requirements in smaller chunks to avoid max_allowed_packet issues
        Log::info("📝 PLAN CREATION: Saving success requirements", [
            'plan_id' => $plan->id,
            'requirements_count' => count($successRequirementsData),
            'requirement_types' => array_unique(array_column($successRequirementsData, 'type'))
        ]);
        
        // Insert in chunks of 50 to avoid packet size issues
        foreach (array_chunk($successRequirementsData, 50) as $chunk) {
            SuccessRequirements::insert($chunk);
        }
        
        Log::info("✅ PLAN CREATION: Success requirements saved", [
            'plan_id' => $plan->id,
            'table' => 'success_requirements',
            'records_inserted' => count($successRequirementsData)
        ]);
    }
    
    /**
     * Sanitize plan data for storage - remove large fields that aren't needed for rerun
     * 
     * @param array $planData The original plan data
     * @return array Sanitized plan data
     */
    protected function sanitizePlanDataForStorage($planData)
    {
        // Remove or limit large fields that can cause max_allowed_packet issues
        $sanitized = $planData;
        
        // Remove available_companies as it's only needed for UI and can be regenerated
        if (isset($sanitized['available_companies'])) {
            unset($sanitized['available_companies']);
        }
        
        // Limit array sizes to prevent massive JSON
        $arrayFields = ['target_roles', 'companies', 'country', 'skills', 'qualifications', 'step_up_candidates', 'alternative_roles_titles', 'acronyms'];
        foreach ($arrayFields as $field) {
            if (isset($sanitized[$field]) && is_array($sanitized[$field])) {
                // Limit to first 100 items to prevent massive payloads
                $sanitized[$field] = array_slice($sanitized[$field], 0, 100);
            }
        }
        
        Log::info("📦 PLAN DATA: Sanitized plan data for storage", [
            'original_size' => strlen(json_encode($planData)),
            'sanitized_size' => strlen(json_encode($sanitized)),
            'reduction_kb' => round((strlen(json_encode($planData)) - strlen(json_encode($sanitized))) / 1024, 2)
        ]);
        
        return $sanitized;
    }

    /**
     * Create notification for plan creation
     * 
     * @param array $planData The plan data
     * @param SuccessionPlan $plan The created plan
     * @param object $user The current user
     * @return void
     */
    protected function createPlanNotification($planData, $plan, $user)
    {
        notifications::create([
            'type'              => "Plan_Created",
            'plan_id'           => $plan->id,
            'entity_name'       => $planData['plan_name'],
            'description'       => $planData['description'],
            'user_id'           => $user->id,
            'user_company'      => $user->company_id
        ]);
    }
}