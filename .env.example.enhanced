# Enhanced Environment Configuration for Redis, Horizon & Queues
# Copy relevant sections to your .env file

# Application
APP_NAME=SuccessionPlanAI
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=successionplan_ai
DB_USERNAME=root
DB_PASSWORD=

# Redis Configuration (Enhanced)
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_CLIENT=phpredis
REDIS_DB=0
REDIS_CACHE_DB=1
REDIS_SESSION_DB=2

# Redis Performance Settings
REDIS_SERIALIZER=igbinary
REDIS_COMPRESSION=lz4
REDIS_CLUSTER=redis
REDIS_PREFIX=successionplan_

# Queue Configuration
QUEUE_CONNECTION=redis
REDIS_QUEUE=default

# Queue Blocking Settings (Performance Optimization)
REDIS_QUEUE_BLOCK_FOR=5
REDIS_INTERNAL_BLOCK_FOR=2
REDIS_EXTERNAL_BLOCK_FOR=10
REDIS_SUCCESSION_BLOCK_FOR=3

# Horizon Configuration
HORIZON_ADMINS=<EMAIL>,<EMAIL>
HORIZON_PREFIX=successionplan_horizon
HORIZON_PATH=horizon
HORIZON_DOMAIN=

# Cache Configuration
CACHE_DRIVER=redis
CACHE_PREFIX=successionplan_cache

# Session Configuration
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null
SESSION_SECURE_COOKIE=false

# Broadcasting (if using)
BROADCAST_DRIVER=redis
PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Logging
LOG_CHANNEL=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Additional Redis Connections (if needed)
REDIS_SENTINEL_SERVICE=mymaster
REDIS_SENTINEL_HOST_1=127.0.0.1:26379
REDIS_SENTINEL_HOST_2=127.0.0.1:26380
REDIS_SENTINEL_HOST_3=127.0.0.1:26381

# Production-specific settings (for .env.production)
# REDIS_CLUSTER_ENABLED=true
# REDIS_CLUSTER_NODES=127.0.0.1:7000,127.0.0.1:7001,127.0.0.1:7002
# REDIS_PASSWORD=your_strong_production_password
# HORIZON_DOMAIN=horizon.yourcompany.com
