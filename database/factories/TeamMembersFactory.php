<?php

namespace Database\Factories;

use App\Models\TeamMembers;
use Illuminate\Database\Eloquent\Factories\Factory;

class TeamMembersFactory extends Factory
{
    protected $model = TeamMembers::class;

    public function definition(): array
    {
        return [
            'user_id' => 1, // Will be overridden in tests
            'team_id' => $this->faker->numberBetween(1, 10),
            'role' => $this->faker->randomElement(['Member', 'Lead', 'Manager']),
            'joined_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
