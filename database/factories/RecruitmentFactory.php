<?php

namespace Database\Factories;

use App\Models\Recruitment;
use Illuminate\Database\Eloquent\Factories\Factory;

class RecruitmentFactory extends Factory
{
    protected $model = Recruitment::class;

    public function definition(): array
    {
        return [
            'user_id' => 1, // Will be overridden in tests
            'name' => $this->faker->jobTitle . ' Recruitment',
            'description' => $this->faker->paragraph(),
            'status' => $this->faker->randomElement(['active', 'draft', 'closed', 'paused']),
            'start_date' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'end_date' => $this->faker->dateTimeBetween('now', '+3 months'),
            'budget' => $this->faker->numberBetween(50000, 200000),
            'priority' => $this->faker->randomElement(['low', 'medium', 'high']),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
