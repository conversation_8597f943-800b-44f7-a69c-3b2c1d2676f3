<?php

namespace Database\Factories;

use App\Models\Account;
use Illuminate\Database\Eloquent\Factories\Factory;

class AccountFactory extends Factory
{
    protected $model = Account::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->company,
            'company_of_interest' => $this->faker->optional()->randomElements(['Company A', 'Company B', 'Company C'], 2),
            'industry_interest' => $this->faker->optional()->randomElements(['Technology', 'Finance', 'Healthcare'], 2),
            'sector_interest' => $this->faker->optional()->randomElements(['Software', 'Banking', 'Insurance'], 2),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
