<?php

namespace Database\Factories;

use App\Models\SuccessRequirements;
use Illuminate\Database\Eloquent\Factories\Factory;

class SuccessRequirementsFactory extends Factory
{
    protected $model = SuccessRequirements::class;

    public function definition(): array
    {
        return [
            'plan_id' => 1, // Will be overridden in tests
            'skill' => $this->faker->randomElement(['PHP Development', 'Project Management', 'Leadership', 'Communication']),
            'experience' => $this->faker->randomElement(['2+ years', '3+ years', '5+ years', '10+ years']),
            'education' => $this->faker->randomElement(['Bachelor\'s Degree', 'Master\'s Degree', 'MBA', 'Certification']),
            'level' => $this->faker->randomElement(['Junior', 'Mid', 'Senior', 'Lead']),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
