<?php

namespace Database\Factories;

use App\Models\SuccessPeople;
use Illuminate\Database\Eloquent\Factories\Factory;

class SuccessPeopleFactory extends Factory
{
    protected $model = SuccessPeople::class;

    public function definition(): array
    {
        return [
            'plan_id' => 1, // Will be overridden in tests
            'name' => $this->faker->name,
            'company' => $this->faker->company,
            'position' => $this->faker->jobTitle,
            'score' => $this->faker->numberBetween(60, 100),
            'experience' => $this->faker->randomElement(['2+ years', '3+ years', '5+ years', '10+ years']),
            'location' => $this->faker->city,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
