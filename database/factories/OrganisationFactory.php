<?php

namespace Database\Factories;

use App\Models\Organisation;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrganisationFactory extends Factory
{
    protected $model = Organisation::class;

    public function definition(): array
    {
        return [
            'user_id' => 1, // Will be overridden in tests
            'name' => $this->faker->jobTitle,
            'role' => $this->faker->randomElement(['Manager', 'Director', 'VP', 'Senior Manager', 'Lead']),
            'department' => $this->faker->randomElement(['Engineering', 'Marketing', 'Sales', 'HR', 'Finance']),
            'level' => $this->faker->numberBetween(1, 10),
            'reports_to' => $this->faker->optional()->numberBetween(1, 100),
            'location' => $this->faker->city,
            'start_date' => $this->faker->dateTimeBetween('-5 years', 'now'),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
