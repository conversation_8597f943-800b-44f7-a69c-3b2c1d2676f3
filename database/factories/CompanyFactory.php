<?php

namespace Database\Factories;

use App\Models\Company;
use Illuminate\Database\Eloquent\Factories\Factory;

class CompanyFactory extends Factory
{
    protected $model = Company::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->company,
            'industry' => $this->faker->randomElement(['Technology', 'Finance', 'Healthcare', 'Manufacturing']),
            'sector' => $this->faker->randomElement(['Software', 'Banking', 'Insurance', 'Automotive']),
            'size' => $this->faker->randomElement(['Small', 'Medium', 'Large']),
            'location' => $this->faker->city,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
