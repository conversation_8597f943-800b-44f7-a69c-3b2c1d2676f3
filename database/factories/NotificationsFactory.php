<?php

namespace Database\Factories;

use App\Models\notifications;
use Illuminate\Database\Eloquent\Factories\Factory;

class NotificationsFactory extends Factory
{
    protected $model = notifications::class;

    public function definition(): array
    {
        return [
            'user_id' => 1, // Will be overridden in tests
            'type' => $this->faker->randomElement(['Plan Created', 'Search Completed', 'Report Generated', 'Upload Finished']),
            'message' => $this->faker->sentence(),
            'data' => json_encode(['additional' => 'data']),
            'read_at' => $this->faker->optional()->dateTime(),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
