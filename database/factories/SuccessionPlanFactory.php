<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SuccessionPlan>
 */
class SuccessionPlanFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->jobTitle,
            'description' => $this->faker->sentence(10),
            'minimum_Experience' => $this->faker->randomElement(['2+ years', '3+ years', '5+ years', '10+ years']),
            'step_up' => $this->faker->randomElement(['Immediate', '6 months', '1 year', '2 years']),
            'ethnicity' => $this->faker->randomElement(['diverse', 'any', 'specific']),
            'tagged_individual' => $this->faker->optional()->name,
            'status' => $this->faker->randomElement(['pending', 'active', 'completed', 'closed']),
            'shared_with' => $this->faker->optional()->email,
            'user_id' => 1, // Will be overridden in tests
            'age' => $this->faker->randomElement(['25-35', '30-40', '35-45', '40-50']),
            'last_opened' => $this->faker->optional()->dateTimeBetween('-30 days', 'now'),
            'candidate_status' => $this->faker->randomElement(['active', 'inactive', 'pending']),
            'mover' => $this->faker->boolean(),
            'created_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'updated_at' => function (array $attributes) {
                return $this->faker->dateTimeBetween($attributes['created_at'], 'now');
            }
        ];
    }
}
