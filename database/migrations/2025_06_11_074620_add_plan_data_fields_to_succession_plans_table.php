<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('succession_plans', function (Blueprint $table) {
            // Core plan fields
            $table->string('plan_name')->nullable()->after('name');
            $table->json('target_roles')->nullable()->after('plan_name');
            $table->json('companies')->nullable()->after('target_roles');
            $table->json('country')->nullable()->after('companies');
            $table->string('gender')->nullable()->after('country');
            $table->integer('minimum_tenure')->nullable()->after('gender');
            $table->boolean('include_alumni')->default(false)->after('minimum_tenure');
            
            // Additional fields
            $table->json('skills')->nullable()->after('include_alumni');
            $table->json('qualifications')->nullable()->after('skills');
            $table->json('step_up_candidates')->nullable()->after('qualifications');
            $table->json('alternative_roles_titles')->nullable()->after('step_up_candidates');
            $table->json('acronyms')->nullable()->after('alternative_roles_titles');
            
            // Store the complete plan data JSON for easy retrieval
            $table->json('plan_data')->nullable()->after('job_description');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('succession_plans', function (Blueprint $table) {
            $table->dropColumn([
                'plan_name',
                'target_roles',
                'companies',
                'country',
                'gender',
                'minimum_tenure',
                'include_alumni',
                'skills',
                'qualifications',
                'step_up_candidates',
                'alternative_roles_titles',
                'acronyms',
                'plan_data'
            ]);
        });
    }
};